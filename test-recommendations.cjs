#!/usr/bin/env node

/**
 * Test script for recommendation feature
 */

const Database = require('better-sqlite3');
const fs = require('fs').promises;

async function setupTestDatabase() {
    console.log('🗄️ Setting up test database for recommendations...');
    
    const db = new Database(':memory:');
    
    // Create required tables
    db.exec(`
        CREATE TABLE tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type TEXT NOT NULL,
            description TEXT,
            status TEXT DEFAULT 'pending',
            args TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `);
    
    db.exec(`
        CREATE TABLE media_metadata (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_id INTEGER NOT NULL,
            file_path TEXT NOT NULL,
            file_hash TEXT NOT NULL UNIQUE,
            metadata_json TEXT NOT NULL,
            extracted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            tool_used TEXT NOT NULL
        )
    `);
    
    db.exec(`
        CREATE TABLE media_transcripts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            media_id INTEGER NOT NULL,
            task_id INTEGER NOT NULL,
            transcript_text TEXT NOT NULL,
            language TEXT,
            whisper_model TEXT NOT NULL,
            transcribed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            summary TEXT,
            summary_style TEXT,
            summary_model TEXT,
            summary_generated_at DATETIME
        )
    `);

    // Create Phase 2 tables
    db.exec(`
        CREATE TABLE user_interactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT NOT NULL,
            media_id INTEGER NOT NULL,
            action TEXT NOT NULL,
            timestamp INTEGER NOT NULL,
            session_id TEXT,
            metadata TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `);

    db.exec(`
        CREATE TABLE content_recommendations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            source_media_id INTEGER,
            source_user_id TEXT,
            recommended_media_id INTEGER NOT NULL,
            recommendation_type TEXT NOT NULL,
            score REAL NOT NULL,
            reason TEXT,
            algorithm_version TEXT,
            generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME
        )
    `);
    
    console.log('✅ Test database created');
    return db;
}

async function insertTestData(db) {
    console.log('📝 Inserting test data...');
    
    // Insert test tasks
    const taskStmt = db.prepare('INSERT INTO tasks (type, description, status) VALUES (?, ?, ?)');
    
    // Insert test media with different characteristics
    const mediaData = [
        {
            filename: 'cooking-tutorial.mp4',
            duration: 600,
            format: 'mp4',
            tags: ['cooking', 'tutorial', 'food'],
            transcript: 'Welcome to our cooking tutorial. Today we will learn how to make pasta. First, boil water in a large pot.',
            summary: '• Cooking tutorial for making pasta\n• Step-by-step instructions\n• Beginner-friendly approach'
        },
        {
            filename: 'travel-vlog-italy.mp4',
            duration: 900,
            format: 'mp4',
            tags: ['travel', 'italy', 'vlog'],
            transcript: 'Hello everyone! Today we are exploring the beautiful streets of Rome. The architecture here is amazing.',
            summary: '• Travel vlog from Rome, Italy\n• Showcases local architecture\n• Personal travel experience'
        },
        {
            filename: 'tech-review-phone.mp4',
            duration: 480,
            format: 'mp4',
            tags: ['technology', 'review', 'smartphone'],
            transcript: 'This is our review of the latest smartphone. The camera quality is impressive and the battery life is excellent.',
            summary: '• Smartphone review and analysis\n• Focus on camera and battery\n• Positive overall assessment'
        },
        {
            filename: 'cooking-dessert.mp4',
            duration: 720,
            format: 'mp4',
            tags: ['cooking', 'dessert', 'baking'],
            transcript: 'Today we are making a delicious chocolate cake. The secret ingredient is high-quality cocoa powder.',
            summary: '• Chocolate cake baking tutorial\n• Emphasizes quality ingredients\n• Dessert preparation guide'
        },
        {
            filename: 'travel-japan.mp4',
            duration: 1200,
            format: 'mp4',
            tags: ['travel', 'japan', 'culture'],
            transcript: 'Welcome to Japan! We are visiting traditional temples and experiencing the local culture and cuisine.',
            summary: '• Japan travel documentary\n• Traditional temples and culture\n• Local cuisine exploration'
        }
    ];

    const mediaIds = [];
    
    for (let i = 0; i < mediaData.length; i++) {
        const data = mediaData[i];
        
        // Insert task
        const taskResult = taskStmt.run('media_ingest', `Ingest ${data.filename}`, 'completed');
        const taskId = taskResult.lastInsertRowid;
        
        // Insert media metadata
        const mediaStmt = db.prepare('INSERT INTO media_metadata (task_id, file_path, file_hash, metadata_json, tool_used) VALUES (?, ?, ?, ?, ?)');
        const mediaResult = mediaStmt.run(
            taskId,
            `/test/${data.filename}`,
            `hash-${i + 1}`,
            JSON.stringify({
                filename: data.filename,
                duration: data.duration,
                format: data.format,
                tags: data.tags
            }),
            'ffprobe'
        );
        const mediaId = mediaResult.lastInsertRowid;
        mediaIds.push(mediaId);
        
        // Insert transcript
        const transcriptStmt = db.prepare('INSERT INTO media_transcripts (media_id, task_id, transcript_text, language, whisper_model, summary) VALUES (?, ?, ?, ?, ?, ?)');
        transcriptStmt.run(mediaId, taskId, data.transcript, 'en', 'turbo', data.summary);
    }

    // Insert user interactions
    const interactionStmt = db.prepare('INSERT INTO user_interactions (user_id, media_id, action, timestamp) VALUES (?, ?, ?, ?)');
    
    // User 'alice' likes cooking content
    interactionStmt.run('alice', mediaIds[0], 'play', Date.now() - 86400000); // 1 day ago
    interactionStmt.run('alice', mediaIds[0], 'complete', Date.now() - 86400000 + 600000); // completed watching
    interactionStmt.run('alice', mediaIds[0], 'like', Date.now() - 86400000 + 700000);
    interactionStmt.run('alice', mediaIds[3], 'play', Date.now() - 43200000); // 12 hours ago
    interactionStmt.run('alice', mediaIds[3], 'complete', Date.now() - 43200000 + 720000);
    
    // User 'bob' likes travel content
    interactionStmt.run('bob', mediaIds[1], 'play', Date.now() - *********); // 2 days ago
    interactionStmt.run('bob', mediaIds[1], 'complete', Date.now() - ********* + 900000);
    interactionStmt.run('bob', mediaIds[1], 'like', Date.now() - ********* + 1000000);
    interactionStmt.run('bob', mediaIds[4], 'play', Date.now() - 86400000); // 1 day ago
    interactionStmt.run('bob', mediaIds[4], 'complete', Date.now() - 86400000 + 1200000);
    
    console.log(`✅ Test data inserted - ${mediaIds.length} media items, user interactions for alice and bob`);
    return mediaIds;
}

async function testSimilarityCalculation(db, mediaIds) {
    console.log('🧮 Testing similarity calculation...');
    
    // Get media metadata for similarity testing
    const media1 = db.prepare('SELECT mm.*, mt.summary FROM media_metadata mm JOIN media_transcripts mt ON mm.id = mt.media_id WHERE mm.id = ?').get(mediaIds[0]);
    const media2 = db.prepare('SELECT mm.*, mt.summary FROM media_metadata mm JOIN media_transcripts mt ON mm.id = mt.media_id WHERE mm.id = ?').get(mediaIds[3]);
    const media3 = db.prepare('SELECT mm.*, mt.summary FROM media_metadata mm JOIN media_transcripts mt ON mm.id = mt.media_id WHERE mm.id = ?').get(mediaIds[1]);
    
    const metadata1 = JSON.parse(media1.metadata_json);
    const metadata2 = JSON.parse(media2.metadata_json);
    const metadata3 = JSON.parse(media3.metadata_json);
    
    console.log(`📊 Comparing media:`);
    console.log(`   1. ${metadata1.filename} (${metadata1.tags.join(', ')})`);
    console.log(`   2. ${metadata2.filename} (${metadata2.tags.join(', ')})`);
    console.log(`   3. ${metadata3.filename} (${metadata3.tags.join(', ')})`);
    
    // Simple similarity calculation (similar to the service)
    function calculateSimilarity(meta1, meta2) {
        let score = 0;
        let factors = 0;
        
        // Format similarity
        if (meta1.format === meta2.format) {
            score += 0.3;
        }
        factors++;
        
        // Duration similarity
        const durationRatio = Math.min(meta1.duration, meta2.duration) / Math.max(meta1.duration, meta2.duration);
        score += durationRatio * 0.2;
        factors++;
        
        // Tags similarity
        const commonTags = meta1.tags.filter(tag => meta2.tags.includes(tag));
        const tagSimilarity = commonTags.length / Math.max(meta1.tags.length, meta2.tags.length);
        score += tagSimilarity * 0.5;
        factors++;
        
        return score / factors;
    }
    
    const sim1_2 = calculateSimilarity(metadata1, metadata2);
    const sim1_3 = calculateSimilarity(metadata1, metadata3);
    
    console.log(`📈 Similarity scores:`);
    console.log(`   Cooking tutorial vs Cooking dessert: ${Math.round(sim1_2 * 100)}%`);
    console.log(`   Cooking tutorial vs Travel vlog: ${Math.round(sim1_3 * 100)}%`);
    
    console.log('✅ Similarity calculation test completed');
}

async function testUserProfileBuilding(db) {
    console.log('👤 Testing user profile building...');
    
    // Get user interactions
    const aliceInteractions = db.prepare(`
        SELECT ui.*, mm.metadata_json 
        FROM user_interactions ui 
        JOIN media_metadata mm ON ui.media_id = mm.id 
        WHERE ui.user_id = 'alice'
    `).all();
    
    const bobInteractions = db.prepare(`
        SELECT ui.*, mm.metadata_json 
        FROM user_interactions ui 
        JOIN media_metadata mm ON ui.media_id = mm.id 
        WHERE ui.user_id = 'bob'
    `).all();
    
    // Build profiles
    function buildProfile(interactions) {
        const profile = {
            preferredFormats: new Map(),
            preferredTags: new Map(),
            totalInteractions: interactions.length
        };
        
        for (const interaction of interactions) {
            const metadata = JSON.parse(interaction.metadata_json);
            const weight = interaction.action === 'like' ? 2.0 : interaction.action === 'complete' ? 1.5 : 1.0;
            
            // Track format preferences
            if (metadata.format) {
                const current = profile.preferredFormats.get(metadata.format) || 0;
                profile.preferredFormats.set(metadata.format, current + weight);
            }
            
            // Track tag preferences
            if (metadata.tags) {
                for (const tag of metadata.tags) {
                    const current = profile.preferredTags.get(tag) || 0;
                    profile.preferredTags.set(tag, current + weight);
                }
            }
        }
        
        return profile;
    }
    
    const aliceProfile = buildProfile(aliceInteractions);
    const bobProfile = buildProfile(bobInteractions);
    
    console.log(`👩 Alice's profile:`);
    console.log(`   Interactions: ${aliceProfile.totalInteractions}`);
    console.log(`   Top tags: ${Array.from(aliceProfile.preferredTags.entries()).sort((a, b) => b[1] - a[1]).slice(0, 3).map(([tag, score]) => `${tag} (${score})`).join(', ')}`);
    
    console.log(`👨 Bob's profile:`);
    console.log(`   Interactions: ${bobProfile.totalInteractions}`);
    console.log(`   Top tags: ${Array.from(bobProfile.preferredTags.entries()).sort((a, b) => b[1] - a[1]).slice(0, 3).map(([tag, score]) => `${tag} (${score})`).join(', ')}`);
    
    console.log('✅ User profile building test completed');
}

async function testRecommendationTask(db, mediaIds) {
    console.log('📋 Testing recommendation task creation...');
    
    // Create a recommendation task
    const taskStmt = db.prepare('INSERT INTO tasks (type, description, status, args) VALUES (?, ?, ?, ?)');
    const taskResult = taskStmt.run(
        'media_recommend',
        'Generate similar recommendations for media ID 1',
        'pending',
        JSON.stringify({
            media_id: mediaIds[0],
            recommendation_type: 'similar',
            top_k: 3
        })
    );
    const taskId = taskResult.lastInsertRowid;
    
    console.log(`✅ Recommendation task created - Task ID: ${taskId}`);
    
    // Simulate storing recommendation results
    const recStmt = db.prepare(`
        INSERT INTO content_recommendations 
        (source_media_id, recommended_media_id, recommendation_type, score, reason, algorithm_version, expires_at)
        VALUES (?, ?, ?, ?, ?, ?, datetime('now', '+24 hours'))
    `);
    
    // Add some mock recommendations
    recStmt.run(mediaIds[0], mediaIds[3], 'similar', 0.85, 'Both are cooking tutorials', 'v1.0');
    recStmt.run(mediaIds[0], mediaIds[2], 'similar', 0.45, 'Similar video format', 'v1.0');
    
    console.log('✅ Mock recommendations stored');
    
    // Verify recommendations
    const recommendations = db.prepare(`
        SELECT cr.*, mm.metadata_json 
        FROM content_recommendations cr 
        JOIN media_metadata mm ON cr.recommended_media_id = mm.id 
        WHERE cr.source_media_id = ?
        ORDER BY cr.score DESC
    `).all(mediaIds[0]);
    
    console.log(`📊 Stored recommendations for media ${mediaIds[0]}:`);
    recommendations.forEach((rec, index) => {
        const metadata = JSON.parse(rec.metadata_json);
        console.log(`   ${index + 1}. ${metadata.filename} (${Math.round(rec.score * 100)}% - ${rec.reason})`);
    });
    
    console.log('✅ Recommendation task test completed');
}

async function main() {
    console.log('🚀 Atlas Recommendation Feature Test');
    console.log('====================================\n');
    
    try {
        const db = await setupTestDatabase();
        const mediaIds = await insertTestData(db);
        await testSimilarityCalculation(db, mediaIds);
        await testUserProfileBuilding(db);
        await testRecommendationTask(db, mediaIds);
        
        console.log('\n🎉 Recommendation feature test completed successfully!');
        console.log('\n📋 Summary of tested features:');
        console.log('   ✅ Database schema for recommendations');
        console.log('   ✅ User interaction tracking');
        console.log('   ✅ Content similarity calculation');
        console.log('   ✅ User profile building');
        console.log('   ✅ Recommendation task creation');
        console.log('   ✅ Recommendation storage and retrieval');
        
        db.close();
        
    } catch (error) {
        console.error('\n❌ Test failed:', error);
        process.exit(1);
    }
}

main();
