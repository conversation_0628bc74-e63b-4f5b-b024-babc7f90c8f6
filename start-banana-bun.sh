#!/bin/bash

# 🍌 Banana Bun Complete Startup Script
# This script starts all required services and the main application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_service() {
    echo -e "${BLUE}[SERVICE]${NC} $1"
}

print_banana() {
    echo -e "${YELLOW}[🍌 BANANA BUN]${NC} $1"
}

# Function to check if a service is running
check_service() {
    local url=$1
    local name=$2
    
    if curl -s "$url" > /dev/null 2>&1; then
        print_status "$name is already running"
        return 0
    else
        return 1
    fi
}

# Function to wait for service to start
wait_for_service() {
    local url=$1
    local name=$2
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for $name to start..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            print_status "$name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$name failed to start within 60 seconds"
    return 1
}

# Function to start a service in a new terminal
start_in_terminal() {
    local command=$1
    local title=$2
    
    if command -v gnome-terminal &> /dev/null; then
        gnome-terminal --title="$title" -- bash -c "$command; exec bash"
    elif command -v xterm &> /dev/null; then
        xterm -title "$title" -e "$command; bash" &
    elif command -v konsole &> /dev/null; then
        konsole --title "$title" -e bash -c "$command; exec bash" &
    else
        print_warning "No terminal emulator found. Running $title in background..."
        nohup bash -c "$command" > /dev/null 2>&1 &
    fi
}

echo ""
echo -e "${YELLOW}🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌${NC}"
echo -e "${YELLOW}🍌                                    🍌${NC}"
echo -e "${YELLOW}🍌        BANANA BUN STARTUP         🍌${NC}"
echo -e "${YELLOW}🍌   A bun-sized AI assistant for    🍌${NC}"
echo -e "${YELLOW}🍌    organizing your digital life   🍌${NC}"
echo -e "${YELLOW}🍌                                    🍌${NC}"
echo -e "${YELLOW}🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌🍌${NC}"
echo ""

print_banana "Starting all services and the main application..."

# Create necessary directories
mkdir -p ./chroma_db ./meilisearch_db ./logs

# Start Ollama service
print_service "Starting Ollama..."
if ! check_service "http://localhost:11434/api/tags" "Ollama"; then
    if command -v ollama &> /dev/null; then
        start_in_terminal "ollama serve" "Ollama Server"
        wait_for_service "http://localhost:11434/api/tags" "Ollama"
        
        # Pull required models if not already present
        print_status "Checking for required Ollama models..."
        if ! ollama list | grep -q "qwen3:8b"; then
            print_status "Pulling qwen3:8b model (this may take a while)..."
            ollama pull qwen3:8b
        fi
    else
        print_error "Ollama is not installed. Please install it first."
        exit 1
    fi
fi

# Start ChromaDB
print_service "Starting ChromaDB..."
if ! check_service "http://localhost:8000/api/v1/heartbeat" "ChromaDB"; then
    if command -v chroma &> /dev/null; then
        start_in_terminal "chroma run --path ./chroma_db --port 8000" "ChromaDB Server"
        wait_for_service "http://localhost:8000/api/v1/heartbeat" "ChromaDB"
    else
        print_error "ChromaDB is not installed. Please install it: pip3 install chromadb"
        exit 1
    fi
fi

# Start MeiliSearch
print_service "Starting MeiliSearch..."
if ! check_service "http://localhost:7700/health" "MeiliSearch"; then
    if command -v meilisearch &> /dev/null; then
        start_in_terminal "meilisearch --db-path ./meilisearch_db --http-addr 127.0.0.1:7700" "MeiliSearch Server"
        wait_for_service "http://localhost:7700/health" "MeiliSearch"
    else
        print_error "MeiliSearch is not installed. Please install it first."
        exit 1
    fi
fi

print_status "✅ All external services are running!"
echo ""
echo "🔍 Service Status:"
echo "   Ollama:      http://localhost:11434"
echo "   ChromaDB:    http://localhost:8000"
echo "   MeiliSearch: http://localhost:7700"
echo ""

# Start the main Banana Bun application
print_banana "Starting Banana Bun main application..."
print_status "This will start the file watcher and task processor"
print_status "Press Ctrl+C to stop the application"
echo ""

# Run the main application
bun run dev
