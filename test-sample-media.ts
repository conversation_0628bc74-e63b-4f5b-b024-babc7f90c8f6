#!/usr/bin/env bun

/**
 * Add sample media data for testing MeiliSearch MCP server
 */

import { meilisearchService } from './src/services/meilisearch-service';
import { initDatabase } from './src/db';

async function addSampleMediaData() {
    console.log('🎬 Adding sample media data for MCP testing...\n');

    try {
        await initDatabase();
        await meilisearchService.initialize();

        const sampleMedia = [
            {
                id: 1,
                metadata: {
                    filename: 'funny-cats-compilation.mp4',
                    filepath: '/media/videos/funny-cats-compilation.mp4',
                    filesize: 50000000,
                    file_hash: 'hash-001',
                    format: 'mp4',
                    duration: 300,
                    bitrate: 2000,
                    title: 'Funny Cats Compilation',
                    description: 'A hilarious compilation of cats doing funny things',
                    tags: ['funny', 'cats', 'compilation', 'pets', 'humor'],
                    transcript: 'Look at this cat jumping around. So funny! The cat is playing with a ball. Another cat is sleeping in a box.',
                    guessed_type: 'video' as const,
                    language: 'en'
                }
            },
            {
                id: 2,
                metadata: {
                    filename: 'cooking-tutorial-pasta.mp4',
                    filepath: '/media/videos/cooking-tutorial-pasta.mp4',
                    filesize: 75000000,
                    file_hash: 'hash-002',
                    format: 'mp4',
                    duration: 600,
                    bitrate: 2500,
                    title: 'How to Cook Perfect Pasta',
                    description: 'Learn the secrets of cooking perfect pasta every time',
                    tags: ['cooking', 'tutorial', 'pasta', 'food', 'recipe'],
                    transcript: 'Today we are going to learn how to cook perfect pasta. First, bring water to a boil. Add salt to the water. Then add the pasta.',
                    guessed_type: 'video' as const,
                    language: 'en'
                }
            },
            {
                id: 3,
                metadata: {
                    filename: 'nature-documentary-ocean.mp4',
                    filepath: '/media/videos/nature-documentary-ocean.mp4',
                    filesize: 120000000,
                    file_hash: 'hash-003',
                    format: 'mp4',
                    duration: 1800,
                    bitrate: 3000,
                    title: 'Ocean Life Documentary',
                    description: 'Explore the mysterious depths of our oceans',
                    tags: ['documentary', 'nature', 'ocean', 'marine life', 'educational'],
                    transcript: 'The ocean covers more than 70 percent of our planet. Deep beneath the waves, countless species thrive in this underwater world.',
                    guessed_type: 'movie' as const,
                    language: 'en'
                }
            },
            {
                id: 4,
                metadata: {
                    filename: 'jazz-music-collection.mp3',
                    filepath: '/media/music/jazz-music-collection.mp3',
                    filesize: 25000000,
                    file_hash: 'hash-004',
                    format: 'mp3',
                    duration: 2400,
                    bitrate: 320,
                    title: 'Classic Jazz Collection',
                    description: 'A collection of timeless jazz classics',
                    tags: ['jazz', 'music', 'classic', 'instrumental', 'relaxing'],
                    artist: 'Various Artists',
                    album: 'Jazz Classics Vol. 1',
                    guessed_type: 'music' as const,
                    language: 'instrumental'
                }
            },
            {
                id: 5,
                metadata: {
                    filename: 'tech-review-smartphone.mp4',
                    filepath: '/media/videos/tech-review-smartphone.mp4',
                    filesize: 40000000,
                    file_hash: 'hash-005',
                    format: 'mp4',
                    duration: 480,
                    bitrate: 1800,
                    title: 'Latest Smartphone Review',
                    description: 'In-depth review of the newest smartphone features',
                    tags: ['technology', 'review', 'smartphone', 'gadgets', 'tech'],
                    transcript: 'This new smartphone has amazing features. The camera quality is outstanding. Battery life lasts all day. The display is crystal clear.',
                    guessed_type: 'video' as const,
                    language: 'en'
                }
            }
        ];

        console.log('📝 Indexing sample media...');
        for (const media of sampleMedia) {
            const documentId = await meilisearchService.indexMedia(media.id, media.metadata);
            console.log(`✅ Indexed: ${media.metadata.title} (ID: ${documentId})`);
        }

        console.log('\n⏳ Waiting for indexing to complete...');
        await new Promise(resolve => setTimeout(resolve, 2000));

        console.log('\n🔍 Testing search with sample data...');
        
        // Test various searches
        const searches = [
            'funny cats',
            'cooking',
            'documentary',
            'music',
            'smartphone'
        ];

        for (const query of searches) {
            const results = await meilisearchService.search(query, { limit: 3 });
            console.log(`\n"${query}": ${results.hits.length} results (${results.processingTimeMs}ms)`);
            results.hits.forEach((hit, i) => {
                console.log(`  ${i + 1}. ${hit.title} (${hit.guessed_type})`);
            });
        }

        console.log('\n🎉 Sample media data added successfully!');
        console.log('\n📋 Added content:');
        console.log('   🎬 Funny Cats Compilation (video)');
        console.log('   👨‍🍳 Cooking Tutorial - Pasta (video)');
        console.log('   🌊 Ocean Life Documentary (movie)');
        console.log('   🎵 Classic Jazz Collection (music)');
        console.log('   📱 Smartphone Review (video)');
        
        console.log('\n🚀 Ready to test MCP smart search features!');
        console.log('Try: bun run smart-search "funny cats"');

    } catch (error) {
        console.error('❌ Failed to add sample data:', error instanceof Error ? error.message : String(error));
        process.exit(1);
    }
}

addSampleMediaData().catch(console.error);
