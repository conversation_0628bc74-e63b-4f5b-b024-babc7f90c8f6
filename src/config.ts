
// Cross-platform default paths
const getDefaultBasePath = () => {
    if (process.platform === 'win32') {
        return 'C:/Users/<USER>/Sync/AI Drop Box';
    } else {
        return process.env.HOME ? `${process.env.HOME}/banana-bun-data` : '/tmp/banana-bun-data';
    }
};

const getDefaultMediaPath = () => {
    if (process.platform === 'win32') {
        return 'E:/';
    } else {
        return process.env.HOME ? `${process.env.HOME}/media` : '/tmp/media';
    }
};

export const BASE_PATH = process.env.BASE_PATH || getDefaultBasePath();
export const MEDIA_COLLECTION_PATH = process.env.MEDIA_COLLECTION_PATH || getDefaultMediaPath();

export const config = {
    paths: {
        incoming: `${BASE_PATH}/incoming`,
        processing: `${BASE_PATH}/processing`,
        archive: `${BASE_PATH}/archive`,
        error: `${BASE_PATH}/error`,
        tasks: `${BASE_PATH}/tasks`,
        outputs: `${BASE_PATH}/outputs`,
        logs: `${BASE_PATH}/logs`,
        dashboard: `${BASE_PATH}/dashboard`,
        database: `${BASE_PATH}/tasks.sqlite`,
        media: `${BASE_PATH}/media`,
        chroma: {
            host: 'localhost',
            port: 8000,
            ssl: false
        }
    },
    openai: {
        apiKey: process.env.OPENAI_API_KEY || '',
        model: 'gpt-4'
    },
    ollama: {
        url: process.env.OLLAMA_URL || 'http://localhost:11434',
        model: process.env.OLLAMA_MODEL || 'qwen3:8b',
        fastModel: process.env.OLLAMA_FAST_MODEL || 'qwen3:8b',
    },
    chromadb: {
        url: process.env.CHROMA_URL,
        tenant: process.env.CHROMA_TENANT,
    },
    meilisearch: {
        url: process.env.MEILISEARCH_URL || 'http://localhost:7700',
        masterKey: process.env.MEILISEARCH_MASTER_KEY,
        indexName: process.env.MEILISEARCH_INDEX_NAME || 'media_index',
    },
    whisper: {
        model: process.env.WHISPER_MODEL || 'turbo',
        device: process.env.WHISPER_DEVICE || 'cpu',
        language: process.env.WHISPER_LANGUAGE || 'auto',
        chunkDuration: parseInt(process.env.WHISPER_CHUNK_DURATION || '30'),
    },
    vision: {
        model: process.env.VISION_MODEL || 'openai/clip-vit-base-patch32',
        frameExtraction: {
            interval: parseInt(process.env.FRAME_INTERVAL_SECONDS || '10'),
            maxFrames: parseInt(process.env.MAX_FRAMES_PER_VIDEO || '50'),
            sceneDetection: process.env.ENABLE_SCENE_DETECTION === 'true',
        },
    },
    s3: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        region: process.env.AWS_DEFAULT_REGION,
        endpoint: process.env.S3_ENDPOINT,
        defaultBucket: process.env.S3_DEFAULT_BUCKET,
        defaultDownloadPath: process.env.S3_DEFAULT_DOWNLOAD_PATH,
        syncLogPath: process.env.S3_SYNC_LOG_PATH,
    },
    media: {
        collectionTv: process.env.MEDIA_COLLECTION_TV,
        collectionMovies: process.env.MEDIA_COLLECTION_MOVIES,
        collectionYouTube: process.env.MEDIA_COLLECTION_YOUTUBE,
        collectionCatchAll: process.env.MEDIA_COLLECTION_CATCHALL,
        tools: {
            ffprobe: process.env.FFPROBE_PATH,
            mediainfo: process.env.MEDIAINFO_PATH,
            preferred: 'ffprobe' as 'ffprobe' | 'mediainfo' | 'auto'
        },
        extensions: {
            video: ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp'],
            audio: ['.mp3', '.flac', '.wav', '.aac', '.ogg', '.m4a', '.wma', '.opus']
        },
        extraction: {
            timeout_ms: 30000,
            max_file_size_mb: 10000,
            enable_deduplication: true
        },
        organize: {
            enabled: true,
            auto_organize_after_ingest: true,
            categorization: {
                useMetadataType: true,
                fallbackToFilename: true,
                defaultCategory: 'catchall' as 'tv' | 'movies' | 'youtube' | 'catchall'
            },
            folderStructure: {
                movies: {
                    pattern: "{title} ({year})",
                    groupByYear: false,
                    groupByGenre: false
                },
                tv: {
                    pattern: "{series}/Season {season:02d}/{series} - S{season:02d}E{episode:02d} - {title}",
                    groupBySeries: true
                },
                youtube: {
                    pattern: "{channel}/{title}",
                    groupByChannel: true
                }
            },
            filenameNormalization: {
                maxLength: 180,
                case: "title" as "title" | "lower" | "upper",
                replaceSpaces: false,
                sanitizeChars: true
            }
        }
    },
    downloaders: {
        ytdlp: {
            path: process.env.YTDLP_PATH || 'yt-dlp',
            defaultFormat: process.env.YTDLP_DEFAULT_FORMAT || 'best[height<=1080]',
            defaultQuality: process.env.YTDLP_DEFAULT_QUALITY || '720p',
            outputTemplate: process.env.YTDLP_OUTPUT_TEMPLATE || '%(title)s [%(id)s].%(ext)s'
        },
        arr: {
            sonarr: {
                baseUrl: process.env.SONARR_BASE_URL,
                apiKey: process.env.SONARR_API_KEY,
                enabled: process.env.SONARR_ENABLED === 'true'
            },
            radarr: {
                baseUrl: process.env.RADARR_BASE_URL,
                apiKey: process.env.RADARR_API_KEY,
                enabled: process.env.RADARR_ENABLED === 'true'
            },
            prowlarr: {
                baseUrl: process.env.PROWLARR_BASE_URL,
                apiKey: process.env.PROWLARR_API_KEY,
                enabled: process.env.PROWLARR_ENABLED === 'true'
            }
        },
        torrent: {
            qbittorrent: {
                baseUrl: process.env.QBITTORRENT_BASE_URL,
                username: process.env.QBITTORRENT_USERNAME,
                password: process.env.QBITTORRENT_PASSWORD,
                enabled: process.env.QBITTORRENT_ENABLED === 'true'
            }
        },
        rss: {
            enabled: process.env.RSS_ENABLED === 'true',
            checkInterval: parseInt(process.env.RSS_CHECK_INTERVAL || '3600'), // seconds
            feeds: process.env.RSS_FEEDS ? process.env.RSS_FEEDS.split(',') : []
        }
    }
};
