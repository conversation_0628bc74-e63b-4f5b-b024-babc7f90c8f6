/**
 * Integration service for *arr applications (Sonarr, Radarr, Prowlarr)
 * Provides API interfaces for requesting and monitoring media downloads
 */

import { config } from '../config';
import { logger } from '../utils/logger';

export interface ArrConfig {
    baseUrl: string;
    apiKey: string;
    enabled: boolean;
}

export interface MediaSearchResult {
    id: number;
    title: string;
    year?: number;
    overview?: string;
    status: string;
    monitored: boolean;
}

export interface Episode {
    id: number;
    seriesId: number;
    seasonNumber: number;
    episodeNumber: number;
    title: string;
    airDate?: string;
    hasFile: boolean;
    monitored: boolean;
}

export interface DownloadStatus {
    id: number;
    title: string;
    status: 'queued' | 'downloading' | 'completed' | 'failed';
    progress: number;
    eta?: string;
    downloadPath?: string;
}

export class ArrIntegration {
    private config: ArrConfig;
    private serviceName: string;

    constructor(serviceName: 'sonarr' | 'radarr' | 'prowlarr') {
        this.serviceName = serviceName;
        this.config = config.downloaders.arr[serviceName];
        
        if (!this.config.enabled) {
            throw new Error(`${serviceName} is not enabled in configuration`);
        }
        
        if (!this.config.baseUrl || !this.config.apiKey) {
            throw new Error(`${serviceName} is not properly configured (missing baseUrl or apiKey)`);
        }
    }

    private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
        const url = `${this.config.baseUrl}/api/v3${endpoint}`;
        
        const response = await fetch(url, {
            ...options,
            headers: {
                'X-Api-Key': this.config.apiKey,
                'Content-Type': 'application/json',
                ...options.headers
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`${this.serviceName} API error (${response.status}): ${errorText}`);
        }

        return response.json();
    }

    /**
     * Search for media by title
     */
    async searchMedia(query: string): Promise<MediaSearchResult[]> {
        try {
            const endpoint = this.serviceName === 'sonarr' ? '/series/lookup' : '/movie/lookup';
            const results = await this.makeRequest(`${endpoint}?term=${encodeURIComponent(query)}`);
            
            return results.map((item: any) => ({
                id: item.id || item.tmdbId || item.tvdbId,
                title: item.title,
                year: item.year,
                overview: item.overview,
                status: item.status || 'unknown',
                monitored: item.monitored || false
            }));
        } catch (error) {
            await logger.error(`Failed to search ${this.serviceName}`, { query, error });
            throw error;
        }
    }

    /**
     * Add media to monitoring (Sonarr/Radarr)
     */
    async addMedia(mediaId: number, title: string, options: {
        monitored?: boolean;
        searchForMissing?: boolean;
        qualityProfileId?: number;
        rootFolderPath?: string;
    } = {}): Promise<boolean> {
        try {
            const endpoint = this.serviceName === 'sonarr' ? '/series' : '/movie';
            
            const payload = {
                [this.serviceName === 'sonarr' ? 'tvdbId' : 'tmdbId']: mediaId,
                title,
                monitored: options.monitored ?? true,
                qualityProfileId: options.qualityProfileId ?? 1,
                rootFolderPath: options.rootFolderPath ?? await this.getDefaultRootFolder(),
                addOptions: {
                    searchForMissing: options.searchForMissing ?? true
                }
            };

            await this.makeRequest(endpoint, {
                method: 'POST',
                body: JSON.stringify(payload)
            });

            await logger.info(`Added media to ${this.serviceName}`, { mediaId, title });
            return true;
        } catch (error) {
            await logger.error(`Failed to add media to ${this.serviceName}`, { mediaId, title, error });
            return false;
        }
    }

    /**
     * Get missing episodes for a series (Sonarr only)
     */
    async getMissingEpisodes(seriesId: number): Promise<Episode[]> {
        if (this.serviceName !== 'sonarr') {
            throw new Error('getMissingEpisodes is only available for Sonarr');
        }

        try {
            const episodes = await this.makeRequest(`/episode?seriesId=${seriesId}`);
            
            return episodes
                .filter((ep: any) => !ep.hasFile && ep.monitored)
                .map((ep: any) => ({
                    id: ep.id,
                    seriesId: ep.seriesId,
                    seasonNumber: ep.seasonNumber,
                    episodeNumber: ep.episodeNumber,
                    title: ep.title,
                    airDate: ep.airDate,
                    hasFile: ep.hasFile,
                    monitored: ep.monitored
                }));
        } catch (error) {
            await logger.error('Failed to get missing episodes', { seriesId, error });
            throw error;
        }
    }

    /**
     * Trigger search for specific media
     */
    async triggerSearch(mediaId: number, seasonNumber?: number): Promise<boolean> {
        try {
            let endpoint = '/command';
            let command: any;

            if (this.serviceName === 'sonarr') {
                command = {
                    name: seasonNumber ? 'SeasonSearch' : 'SeriesSearch',
                    seriesId: mediaId
                };
                if (seasonNumber) {
                    command.seasonNumber = seasonNumber;
                }
            } else {
                command = {
                    name: 'MoviesSearch',
                    movieIds: [mediaId]
                };
            }

            await this.makeRequest(endpoint, {
                method: 'POST',
                body: JSON.stringify(command)
            });

            await logger.info(`Triggered search in ${this.serviceName}`, { mediaId, seasonNumber });
            return true;
        } catch (error) {
            await logger.error(`Failed to trigger search in ${this.serviceName}`, { mediaId, seasonNumber, error });
            return false;
        }
    }

    /**
     * Get download queue status
     */
    async getDownloadQueue(): Promise<DownloadStatus[]> {
        try {
            const queue = await this.makeRequest('/queue');
            
            return queue.records?.map((item: any) => ({
                id: item.id,
                title: item.title,
                status: this.mapDownloadStatus(item.status),
                progress: item.sizeleft ? ((item.size - item.sizeleft) / item.size) * 100 : 0,
                eta: item.timeleft,
                downloadPath: item.outputPath
            })) || [];
        } catch (error) {
            await logger.error(`Failed to get download queue from ${this.serviceName}`, { error });
            return [];
        }
    }

    /**
     * Get system status
     */
    async getSystemStatus(): Promise<{ version: string; isHealthy: boolean }> {
        try {
            const status = await this.makeRequest('/system/status');
            return {
                version: status.version,
                isHealthy: true
            };
        } catch (error) {
            return {
                version: 'unknown',
                isHealthy: false
            };
        }
    }

    private async getDefaultRootFolder(): Promise<string> {
        try {
            const rootFolders = await this.makeRequest('/rootfolder');
            return rootFolders[0]?.path || '/media';
        } catch (error) {
            return '/media';
        }
    }

    private mapDownloadStatus(status: string): DownloadStatus['status'] {
        switch (status?.toLowerCase()) {
            case 'queued':
            case 'paused':
                return 'queued';
            case 'downloading':
                return 'downloading';
            case 'completed':
                return 'completed';
            case 'failed':
            case 'warning':
                return 'failed';
            default:
                return 'queued';
        }
    }
}

// Convenience functions for common operations
export async function requestShow(showName: string): Promise<boolean> {
    try {
        const sonarr = new ArrIntegration('sonarr');
        const results = await sonarr.searchMedia(showName);
        
        if (results.length === 0) {
            await logger.warn('No shows found for query', { showName });
            return false;
        }

        const show = results[0];
        return await sonarr.addMedia(show.id, show.title);
    } catch (error) {
        await logger.error('Failed to request show', { showName, error });
        return false;
    }
}

export async function requestMovie(movieName: string): Promise<boolean> {
    try {
        const radarr = new ArrIntegration('radarr');
        const results = await radarr.searchMedia(movieName);
        
        if (results.length === 0) {
            await logger.warn('No movies found for query', { movieName });
            return false;
        }

        const movie = results[0];
        return await radarr.addMedia(movie.id, movie.title);
    } catch (error) {
        await logger.error('Failed to request movie', { movieName, error });
        return false;
    }
}

export async function checkMissingEpisodes(seriesId: number): Promise<Episode[]> {
    try {
        const sonarr = new ArrIntegration('sonarr');
        return await sonarr.getMissingEpisodes(seriesId);
    } catch (error) {
        await logger.error('Failed to check missing episodes', { seriesId, error });
        return [];
    }
}

export async function triggerSearch(mediaId: number, mediaType: 'tv' | 'movie', seasonNumber?: number): Promise<boolean> {
    try {
        const service = mediaType === 'tv' ? 'sonarr' : 'radarr';
        const arr = new ArrIntegration(service);
        return await arr.triggerSearch(mediaId, seasonNumber);
    } catch (error) {
        await logger.error('Failed to trigger search', { mediaId, mediaType, seasonNumber, error });
        return false;
    }
}
