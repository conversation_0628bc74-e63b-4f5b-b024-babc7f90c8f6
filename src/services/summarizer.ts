import OpenAI from 'openai';
import { config } from '../config';
import { logger } from '../utils/logger';

export interface SummaryOptions {
    style?: 'bullet' | 'paragraph' | 'key_points';
    model?: string;
    maxTokens?: number;
    temperature?: number;
}

export interface SummaryResult {
    success: boolean;
    summary?: string;
    error?: string;
    model_used?: string;
    tokens_used?: number;
    processing_time_ms?: number;
}

export class SummarizerService {
    private openai: OpenAI | null = null;
    private initialized = false;

    constructor() {
        this.initialize();
    }

    private async initialize() {
        try {
            // Check if OpenAI API key is configured
            const apiKey = process.env.OPENAI_API_KEY;
            if (!apiKey) {
                await logger.warn('OpenAI API key not configured, summarization will be disabled');
                return;
            }

            this.openai = new OpenAI({
                apiKey: apiKey
            });

            this.initialized = true;
            await logger.info('Summarizer service initialized successfully');
        } catch (error) {
            await logger.error('Failed to initialize summarizer service', {
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }

    async generateSummary(transcriptText: string, options: SummaryOptions = {}): Promise<SummaryResult> {
        const startTime = Date.now();

        if (!this.initialized || !this.openai) {
            return {
                success: false,
                error: 'Summarizer service not initialized or OpenAI API key not configured'
            };
        }

        if (!transcriptText || transcriptText.trim().length === 0) {
            return {
                success: false,
                error: 'Empty transcript text provided'
            };
        }

        try {
            const {
                style = 'bullet',
                model = 'gpt-3.5-turbo',
                maxTokens = 500,
                temperature = 0.3
            } = options;

            // Create style-specific prompt
            const stylePrompts = {
                bullet: 'Create a concise bullet-point summary of the key points from this transcript:',
                paragraph: 'Write a concise paragraph summary of this transcript:',
                key_points: 'Extract and list the most important key points from this transcript:'
            };

            const systemPrompt = `You are a helpful assistant that creates accurate, concise summaries of media transcripts. 
Focus on the main topics, key information, and important details. Keep the summary informative but brief.`;

            const userPrompt = `${stylePrompts[style]}

Transcript:
${transcriptText}

Please provide a clear, well-structured summary.`;

            await logger.info('Generating summary', {
                transcriptLength: transcriptText.length,
                style,
                model,
                maxTokens
            });

            const response = await this.openai.chat.completions.create({
                model,
                messages: [
                    { role: 'system', content: systemPrompt },
                    { role: 'user', content: userPrompt }
                ],
                max_tokens: maxTokens,
                temperature,
                top_p: 1,
                frequency_penalty: 0,
                presence_penalty: 0
            });

            const summary = response.choices[0]?.message?.content?.trim();
            if (!summary) {
                return {
                    success: false,
                    error: 'No summary generated by the model'
                };
            }

            const processingTime = Date.now() - startTime;
            const tokensUsed = response.usage?.total_tokens || 0;

            await logger.info('Summary generated successfully', {
                summaryLength: summary.length,
                tokensUsed,
                processingTimeMs: processingTime,
                model
            });

            return {
                success: true,
                summary,
                model_used: model,
                tokens_used: tokensUsed,
                processing_time_ms: processingTime
            };

        } catch (error) {
            const processingTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : String(error);

            await logger.error('Failed to generate summary', {
                error: errorMessage,
                processingTimeMs: processingTime,
                transcriptLength: transcriptText.length
            });

            return {
                success: false,
                error: errorMessage,
                processing_time_ms: processingTime
            };
        }
    }

    /**
     * Generate summary for a specific media transcript from database
     */
    async generateSummaryForMedia(mediaId: number, options: SummaryOptions = {}): Promise<SummaryResult & { transcript_id?: number }> {
        try {
            // Import here to avoid circular dependencies
            const { getDatabase } = await import('../db');
            const db = getDatabase();

            // Get transcript for the media
            const transcriptRow = db.prepare(`
                SELECT id, transcript_text 
                FROM media_transcripts 
                WHERE media_id = ? 
                ORDER BY transcribed_at DESC 
                LIMIT 1
            `).get(mediaId) as { id: number; transcript_text: string } | undefined;

            if (!transcriptRow) {
                return {
                    success: false,
                    error: `No transcript found for media ID ${mediaId}`
                };
            }

            const result = await this.generateSummary(transcriptRow.transcript_text, options);
            
            return {
                ...result,
                transcript_id: transcriptRow.id
            };

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            await logger.error('Failed to generate summary for media', {
                mediaId,
                error: errorMessage
            });

            return {
                success: false,
                error: errorMessage
            };
        }
    }

    /**
     * Check if the service is properly initialized
     */
    isInitialized(): boolean {
        return this.initialized;
    }
}

// Export singleton instance
export const summarizerService = new SummarizerService();
