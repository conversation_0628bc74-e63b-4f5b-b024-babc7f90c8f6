---
id: sample-task-1
tool: code_search
args:
    query: 'Find all functions that handle file operations'
    target_directories: ['src/utils']
metadata:
    created_by: 'system'
    priority: 1
    tags: ['search', 'code-analysis']
    markdown_content: |
        # Code Search Task

        This task will search through the codebase for functions that handle file operations.
        The search will focus on the `src/utils` directory.

        ## Expected Results
        - List of functions that perform file operations
        - Their locations in the codebase
        - Brief description of what each function does

        ## Notes
        - Focus on core file operations like read, write, delete
        - Include both synchronous and asynchronous functions
---

This is the main content of the task file. It can contain additional documentation,
examples, or context that might be useful for the task execution.
