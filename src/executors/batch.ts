import type { BaseTask, BatchTask } from '../types';
import { logger } from '../utils/logger';
import { getDatabase } from '../db';
import { generateFolderRenameTasks, type FolderRenameGenerator } from './generators/folder_rename';

export async function executeBatchTask(task: BatchTask): Promise<{ success: boolean; subtaskIds?: number[]; error?: string }> {
    // Handle dynamic batch tasks (with generators)
    if (task.generator) {
        return executeGeneratorBatchTask(task);
    }

    // Static batch tasks should be handled by the task processor
    // This shouldn't happen in the main orchestrator
    const error = 'Static batch tasks should be handled by the task processor';
    await logger.error(error, { task });
    return { success: false, error };
}

async function executeGeneratorBatchTask(task: BaseTask): Promise<{ success: boolean; subtaskIds?: number[]; error?: string }> {
    // Check if task is a BatchTask with a generator
    if (task.type !== 'batch' || !(task as BatchTask).generator) {
        const error = 'No generator found in batch task';
        await logger.error(error, { task });
        return { success: false, error };
    }

    try {
        let generatedSubtasks: any[] = [];
        const batchTask = task as BatchTask;

        // Handle different generator types
        switch (batchTask.generator?.type) {
            case 'folder_rename':
                // We've already checked generator exists above, so we can safely assert it's not undefined
                generatedSubtasks = await generateFolderRenameTasks(batchTask.generator as FolderRenameGenerator);
                break;
            default:
                const error = `Unknown generator type: ${batchTask.generator?.type}`;
                await logger.error(error, { taskId: task.id, generator: batchTask.generator });
                return { success: false, error };
        }

        if (generatedSubtasks.length === 0) {
            const message = `No subtasks generated by ${task.generator?.type} generator`;
            await logger.info(message, { taskId: task.id });
            // This is success - just no work to do

            // Mark parent task as completed since there's nothing to do
            if (typeof task.id === 'number') {
                const db = getDatabase();
                const resultSummary = `No items found to process with ${task.generator?.type} generator`;
                db.run(
                    `UPDATE tasks SET status = 'completed', finished_at = CURRENT_TIMESTAMP, result_summary = ? WHERE id = ?`,
                    [resultSummary, task.id]
                );
            }

            return { success: true, subtaskIds: [] };
        }

        await logger.info(`Generated ${generatedSubtasks.length} subtasks`, {
            taskId: task.id,
            generatorType: task.generator?.type,
            subtasks: generatedSubtasks.map(s => ({ description: s.description, tool: s.tool }))
        });

        // Create subtasks in database
        const db = getDatabase();
        const subtaskIds: number[] = [];

        for (const subtask of generatedSubtasks) {
            // Insert subtask into database
            db.run(
                `INSERT INTO tasks (type, description, status, parent_id, tool, args) VALUES (?, ?, 'pending', ?, ?, ?)`,
                [
                    subtask.type || 'tool',
                    subtask.description,
                    task.id,
                    subtask.tool,
                    JSON.stringify(subtask.args)
                ]
            );

            const result = db.query('SELECT last_insert_rowid() as id').get() as { id: number } | undefined;
            if (result?.id) {
                subtaskIds.push(result.id);
            }
        }

        // Mark parent task with subtask info (not completed yet - will be completed when all subtasks finish)
        if (typeof task.id === 'number') {
            const resultSummary = `Generated ${subtaskIds.length} subtasks using ${task.generator?.type} generator`;
            db.run(
                `UPDATE tasks SET result_summary = ? WHERE id = ?`,
                [resultSummary, task.id]
            );
        }

        await logger.info('Batch task executed successfully', {
            taskId: task.id,
            subtaskIds,
            generatedCount: generatedSubtasks.length
        });

        return { success: true, subtaskIds };

    } catch (err) {
        const error = err instanceof Error ? err.message : String(err);
        await logger.error('Error executing batch task', { taskId: task.id, error });
        return { success: false, error };
    }
} 
