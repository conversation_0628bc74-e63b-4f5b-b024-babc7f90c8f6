# PRD: Remove Torrent-Related Functionality from Atlas

## Executive Summary

This Product Requirements Document (PRD) outlines the removal of all torrent-related functionality from the Atlas media management application to prepare it for open-source distribution. The goal is to eliminate any features that could be associated with copyright infringement while maintaining the core media processing and management capabilities.

## Background

Atlas currently includes torrent-related functionality through integrations with *arr services (Sonarr, Radarr, Prowlarr) and qBittorrent. While these tools have legitimate uses, their primary association with piracy makes them unsuitable for a public open-source project. Removing this functionality will:

- Eliminate legal concerns around copyright infringement
- Make the project more suitable for corporate and educational environments
- Focus the project on its core strengths: media processing, AI-powered analysis, and organization
- Reduce maintenance burden and complexity

## Current Torrent-Related Functionality

### 1. *arr Services Integration (`src/services/arr-integrations.ts`)
- **Sonarr Integration**: TV show search, monitoring, and download requests
- **Radarr Integration**: Movie search, monitoring, and download requests  
- **Prowlarr Integration**: Torrent indexer aggregation and search
- **Features**:
  - Media search by title
  - Adding media to monitoring queues
  - Triggering manual searches
  - Download queue status monitoring
  - Missing episode detection

### 2. Download Executor (`src/executors/download.ts`)
- **Torrent Download Handler**: Processes `media_download` tasks with source `torrent`
- **Integration**: Routes torrent requests to appropriate *arr services
- **Workflow**: Creates follow-up media ingest tasks after successful downloads

### 3. CLI Tool (`src/cli/download-media.ts`)
- **Torrent Commands**: 
  - `--source torrent --query "Movie Title" --media movie`
  - `--source torrent --query "TV Show" --media tv`
- **Simulation Mode**: `--simulate` flag for testing torrent workflows

### 4. Configuration (`src/config.ts`)
- **Sonarr Configuration**: Base URL, API key, enabled flag
- **Radarr Configuration**: Base URL, API key, enabled flag
- **Prowlarr Configuration**: Base URL, API key, enabled flag
- **qBittorrent Configuration**: Base URL, username, password, enabled flag

### 5. Environment Configuration
- **Files**: `.env.example`, `.env.linux`
- **Variables**: All *arr and qBittorrent connection settings

### 6. Documentation
- **Content Discovery Integration**: Extensive torrent workflow documentation
- **Research Documents**: Torrent discovery strategies and implementation notes
- **PRD Part 2**: Torrent integration requirements and specifications

## Scope of Removal

### In Scope
1. **Complete removal** of all *arr service integrations (Sonarr, Radarr, Prowlarr)
2. **Complete removal** of qBittorrent integration
3. **Removal** of torrent-related CLI commands and options
4. **Removal** of torrent download executor functionality
5. **Removal** of all torrent-related configuration options
6. **Update** of documentation to remove torrent references
7. **Cleanup** of environment configuration files

### Out of Scope
1. **YouTube downloading** via yt-dlp (legitimate use case)
2. **RSS feed monitoring** (legitimate use case)
3. **Direct file downloads** from HTTP/HTTPS sources
4. **Core media processing** pipeline (transcription, tagging, organization)
5. **AI-powered features** (embeddings, similarity search, recommendations)

## Implementation Plan

### Phase 1: Code Removal
1. **Remove arr-integrations service**
   - Delete `src/services/arr-integrations.ts`
   - Remove all imports and references

2. **Update download executor**
   - Remove `downloadFromTorrent()` function
   - Remove torrent case from switch statement
   - Update error handling

3. **Update CLI tool**
   - Remove torrent source option
   - Update help text and examples
   - Remove torrent-related validation

4. **Update configuration**
   - Remove all *arr and qBittorrent config sections
   - Clean up type definitions

### Phase 2: Documentation Updates
1. **Update main documentation**
   - Remove torrent sections from README.md
   - Update feature lists and examples

2. **Remove/update specific docs**
   - Update `CONTENT-DISCOVERY-INTEGRATION.md`
   - Remove torrent sections from research documents
   - Update PRD documents

3. **Clean environment files**
   - Remove torrent-related variables from `.env.example`
   - Update `.env.linux` template

### Phase 3: Testing and Validation
1. **Update tests**
   - Remove torrent-related test cases
   - Update integration tests
   - Verify no broken imports

2. **Validate functionality**
   - Ensure YouTube downloads still work
   - Verify RSS monitoring functionality
   - Test media processing pipeline

## Technical Considerations

### Dependencies
- **No package.json changes required**: No torrent-specific dependencies detected
- **Configuration validation**: Update config validation to reject torrent settings
- **Error handling**: Graceful handling of legacy torrent tasks in database

### Database Migration
- **Existing tasks**: Handle existing `media_download` tasks with source `torrent`
- **Options**:
  - Mark as cancelled with appropriate error message
  - Provide migration script to clean up orphaned tasks

### Backward Compatibility
- **Breaking change**: This is an intentional breaking change
- **Migration guide**: Provide documentation for users who need torrent functionality
- **Alternative solutions**: Suggest external tools for users who require this functionality

## Success Criteria

### Functional Requirements
1. ✅ All torrent-related code removed from codebase
2. ✅ YouTube and RSS downloads continue to work
3. ✅ Media processing pipeline unaffected
4. ✅ No broken imports or references
5. ✅ Clean configuration without torrent options

### Documentation Requirements
1. ✅ All torrent references removed from user-facing documentation
2. ✅ Updated examples and tutorials
3. ✅ Clear migration guide for affected users

### Quality Requirements
1. ✅ All tests pass after removal
2. ✅ No dead code or unused imports
3. ✅ Clean git history with clear commit messages

## Risk Assessment

### Low Risk
- **Core functionality**: Media processing pipeline is independent
- **Dependencies**: No external package removals required

### Medium Risk
- **User impact**: Users relying on torrent functionality will need alternatives
- **Configuration**: Existing installations may have torrent settings

### Mitigation Strategies
- **Clear communication**: Document the change and rationale
- **Migration guide**: Provide alternatives and migration steps
- **Gradual rollout**: Consider feature flag for transition period

## Timeline

### Week 1: Code Removal
- Remove arr-integrations service
- Update download executor and CLI
- Update configuration

### Week 2: Documentation and Testing
- Update all documentation
- Clean environment files
- Comprehensive testing

### Week 3: Validation and Release
- Final validation
- Migration guide creation
- Release preparation

## Conclusion

Removing torrent functionality from Atlas will significantly improve its suitability for open-source distribution while maintaining its core value proposition as an AI-powered media management system. The removal is technically straightforward due to the modular architecture, and the remaining functionality (YouTube downloads, RSS monitoring, media processing) provides substantial value for legitimate use cases.
