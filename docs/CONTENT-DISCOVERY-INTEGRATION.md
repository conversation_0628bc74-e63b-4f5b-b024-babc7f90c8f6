# Content Discovery and Downloader Integration

This document describes the implementation of Atlas's Content Discovery and Downloader Integration system, which enables automatic media acquisition through various sources.

## Overview

The Content Discovery and Downloader Integration allows Atlas to:
- Download content from YouTube using yt-dlp
- Request downloads through *arr services (Sonarr, Radarr, Prowlarr)
- Monitor RSS feeds for new content
- Automatically ingest downloaded content into the media pipeline

## Components

### 1. Media Download Task Type

A new task type `media_download` has been added with the following structure:

```typescript
interface MediaDownloadTask {
    type: 'media_download';
    description: string;
    source: 'torrent' | 'nzb' | 'youtube' | 'rss';
    url?: string;
    query?: string;
    media_type?: 'movie' | 'tv' | 'music' | 'video';
    destination_path?: string;
    quality?: string;
    format?: string;
}
```

### 2. Download Executor (`src/executors/download.ts`)

Handles the execution of media download tasks:
- **YouTube Downloads**: Uses yt-dlp to download videos
- **Torrent Downloads**: Integrates with Sonarr/Radarr APIs
- **RSS Downloads**: Downloads direct media files from RSS enclosures
- **NZB Downloads**: Placeholder for future implementation

### 3. CLI Tool (`src/cli/download-media.ts`)

Command-line interface for creating download tasks:

```bash
# Download YouTube video
bun run src/cli/download-media.ts --source youtube --url "https://youtube.com/watch?v=..."

# Request movie via torrent
bun run src/cli/download-media.ts --source torrent --query "Movie Title 2024" --media movie

# Download from RSS
bun run src/cli/download-media.ts --source rss --url "https://feeds.example.com/media.xml"

# Simulate download (testing)
bun run src/cli/download-media.ts --source youtube --url "..." --simulate
```

### 4. *arr Integrations (`src/services/arr-integrations.ts`)

Service for interacting with Sonarr, Radarr, and Prowlarr:
- Search for media content
- Add media to monitoring
- Trigger manual searches
- Check download queue status
- Monitor missing episodes

### 5. RSS Watcher (`src/services/rss-watcher.ts`)

Monitors RSS feeds for new content:
- Periodic feed checking
- New item detection
- Automatic download task creation
- Support for podcast and video feeds

## Configuration

Add the following to your environment variables or configuration:

### yt-dlp Configuration
```bash
YTDLP_PATH=yt-dlp
YTDLP_DEFAULT_FORMAT=best[height<=1080]
YTDLP_DEFAULT_QUALITY=720p
YTDLP_OUTPUT_TEMPLATE=%(title)s [%(id)s].%(ext)s
```

### *arr Services Configuration
```bash
# Sonarr (TV Shows)
SONARR_BASE_URL=http://localhost:8989
SONARR_API_KEY=your_sonarr_api_key
SONARR_ENABLED=true

# Radarr (Movies)
RADARR_BASE_URL=http://localhost:7878
RADARR_API_KEY=your_radarr_api_key
RADARR_ENABLED=true

# Prowlarr (Indexer Manager)
PROWLARR_BASE_URL=http://localhost:9696
PROWLARR_API_KEY=your_prowlarr_api_key
PROWLARR_ENABLED=true
```

### RSS Configuration
```bash
RSS_ENABLED=true
RSS_CHECK_INTERVAL=3600  # seconds
RSS_FEEDS=https://feed1.com/rss,https://feed2.com/rss
```

### qBittorrent Configuration (Optional)
```bash
QBITTORRENT_BASE_URL=http://localhost:8080
QBITTORRENT_USERNAME=admin
QBITTORRENT_PASSWORD=password
QBITTORRENT_ENABLED=true
```

## Usage Examples

### 1. YouTube Downloads

```bash
# Basic YouTube download
bun run src/cli/download-media.ts --source youtube --url "https://youtube.com/watch?v=dQw4w9WgXcQ"

# YouTube download with quality preference
bun run src/cli/download-media.ts --source youtube --url "..." --quality 1080p --format mp4
```

### 2. Torrent Downloads via *arr

```bash
# Request TV show
bun run src/cli/download-media.ts --source torrent --query "Breaking Bad" --media tv

# Request movie
bun run src/cli/download-media.ts --source torrent --query "Inception 2010" --media movie
```

### 3. RSS Monitoring

The RSS watcher runs automatically when enabled. To add feeds programmatically:

```typescript
import { rssWatcher } from './src/services/rss-watcher';

// Add a new feed
rssWatcher.addFeed('https://example.com/podcast.xml');

// Start monitoring
rssWatcher.start();
```

## Integration with Media Pipeline

Downloaded content automatically flows into the existing media pipeline:

1. **Download Completion**: When a download completes, a `media_ingest` task is automatically created
2. **Metadata Extraction**: The media ingest process extracts metadata using FFprobe/MediaInfo
3. **Organization**: Files are organized according to configured rules
4. **Transcription**: Audio/video content is transcribed using Whisper
5. **Indexing**: Content is indexed in MeiliSearch and ChromaDB for search

## API Integration Details

### Sonarr Integration
- **Search**: `/api/v3/series/lookup?term={query}`
- **Add Series**: `POST /api/v3/series`
- **Missing Episodes**: `/api/v3/episode?seriesId={id}`
- **Trigger Search**: `POST /api/v3/command`

### Radarr Integration
- **Search**: `/api/v3/movie/lookup?term={query}`
- **Add Movie**: `POST /api/v3/movie`
- **Trigger Search**: `POST /api/v3/command`

### Download Queue Monitoring
Both services provide queue status via `/api/v3/queue` endpoint for monitoring download progress.

## Error Handling

The system includes comprehensive error handling:
- **Configuration Validation**: Checks for required settings before attempting downloads
- **API Error Handling**: Graceful handling of *arr service failures
- **Download Failures**: Proper error reporting and task status updates
- **Retry Logic**: Configurable retry attempts for failed downloads

## Testing

Run the test suite to validate the integration:

```bash
bun test src/tests/download-integration.test.ts
```

The tests cover:
- Task creation and validation
- Configuration validation
- Database integration
- Source-specific requirements

## Future Enhancements

Planned improvements include:
- Direct qBittorrent/NZBGet integration
- Ombi-style request workflow
- Magnet link preference settings
- AI-powered query rewriting for better search results
- Advanced RSS feed filtering
- Download scheduling and bandwidth management

## Troubleshooting

### Common Issues

1. **yt-dlp not found**: Ensure yt-dlp is installed and in PATH
2. ***arr API errors**: Verify API keys and service URLs
3. **Permission errors**: Check file system permissions for download directories
4. **RSS parsing errors**: Validate RSS feed URLs and formats

### Logs

Check the Atlas logs for detailed error information:
```bash
tail -f logs/atlas.log
```

Download-related logs are tagged with the task ID and source type for easy filtering.
