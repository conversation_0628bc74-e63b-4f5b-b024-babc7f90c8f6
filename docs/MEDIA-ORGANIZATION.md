# 📂 Media Organization System

## Overview

The Media Organization System automatically organizes ingested media files into a clean, consistent folder structure based on extracted metadata and filename patterns. This system integrates seamlessly with the existing media ingestion pipeline and supports Plex/Jellyfin-style libraries.

## Features

### ✅ Implemented Features

- **Automatic Organization**: Files are automatically organized after successful media ingestion
- **Smart Type Detection**: Uses metadata and filename patterns to categorize media (TV, Movies, YouTube, Catch-all)
- **Configurable Folder Structures**: Customizable naming patterns for different media types
- **Filename Normalization**: Sanitizes filenames and handles illegal characters
- **Collision Resolution**: Automatically handles filename conflicts with safe alternatives
- **Batch Processing**: Supports organizing multiple files in a single task
- **Dry Run Mode**: Test organization without actually moving files
- **Database Integration**: Updates file paths in database after successful moves
- **Idempotency**: Tasks can be safely retried without duplicating moves

### 🎯 Media Type Detection

The system automatically detects media types using:

1. **Metadata Analysis**: Uses extracted metadata to identify content type
2. **Filename Patterns**: Analyzes filenames for TV show patterns (S01E01), movie indicators, etc.
3. **Source Context**: Considers the source of the media (YouTube downloads, etc.)
4. **Fallback Logic**: Uses catch-all collection when type cannot be determined

### 📁 Default Folder Structure

```
/media-library/
├── Movies/                  # config.media.collectionMovies
│   └── The Matrix (1999)/
│       └── The Matrix (1999).mkv
├── Shows/                   # config.media.collectionTv
│   └── The Office/
│       └── Season 01/
│           └── The Office - S01E01 - Pilot.mkv
├── YouTube/                 # config.media.collectionYouTube
│   └── Channel Name/
│       └── Video Title.mp4
└── Downloads (temp area)/   # config.media.collectionCatchAll
    └── Uncategorized Media.mp3
```

## Configuration

### Media Organization Settings

Add to your `config.ts`:

```typescript
media: {
    // ... existing media config
    organize: {
        enabled: true,
        auto_organize_after_ingest: true,
        categorization: {
            useMetadataType: true,     // Use metadata to determine type
            fallbackToFilename: true,  // Use filename patterns as fallback
            defaultCategory: 'catchall' // Default when type can't be determined
        },
        folderStructure: {
            movies: {
                pattern: "{title} ({year})",
                groupByYear: false,
                groupByGenre: false
            },
            tv: {
                pattern: "{series}/Season {season:02d}/{series} - S{season:02d}E{episode:02d} - {title}",
                groupBySeries: true
            },
            youtube: {
                pattern: "{channel}/{title}",
                groupByChannel: true
            }
        },
        filenameNormalization: {
            maxLength: 180,            // Max filename length
            case: "title",             // title, lower, upper
            replaceSpaces: false,      // Replace spaces with dots
            sanitizeChars: true        // Remove illegal characters
        }
    }
}
```

### Template Variables

#### TV Shows
- `{series}` - Series name
- `{season}` - Season number
- `{episode}` - Episode number
- `{title}` - Episode title
- `{season:02d}` - Zero-padded season (01, 02, etc.)

#### Movies
- `{title}` - Movie title
- `{year}` - Release year

#### YouTube
- `{channel}` - Channel name
- `{title}` - Video title

## Usage

### Automatic Organization

Media files are automatically organized after successful ingestion when `auto_organize_after_ingest` is enabled.

### Manual Organization

#### CLI Usage

```bash
# Organize a single file
npm run media-organize -- --file-path "/path/to/The.Matrix.1999.mkv"

# Organize multiple files
npm run media-organize -- --file-paths "/path/to/S01E01.mkv,/path/to/S01E02.mkv"

# Force specific collection type
npm run media-organize -- --file-path "/path/to/movie.mkv" --collection movies

# Dry run (test without moving)
npm run media-organize -- --file-path "/path/to/movie.mkv" --dry-run

# Force overwrite existing files
npm run media-organize -- --file-path "/path/to/movie.mkv" --force
```

#### Task File Examples

**Single File Organization:**
```json
{
  "type": "media_organize",
  "description": "Organize movie file",
  "file_path": "/path/to/The.Matrix.1999.mkv"
}
```

**Batch Organization:**
```json
{
  "type": "media_organize",
  "description": "Organize TV season",
  "file_paths": [
    "/path/to/The.Office.S01E01.mkv",
    "/path/to/The.Office.S01E02.mkv"
  ],
  "target_collection": "tv"
}
```

**Dry Run:**
```json
{
  "type": "media_organize",
  "description": "Test organization",
  "file_path": "/path/to/unknown.mkv",
  "dry_run": true
}
```

## Pattern Recognition

### TV Show Detection

The system recognizes these TV show patterns:

- `S01E01` - Standard season/episode format
- `Season 1 Episode 1` - Verbose format
- `1x01` - Alternative format
- `.101.` - Numeric season.episode format

### Movie Detection

Movie detection looks for:

- Year patterns: `(1999)`, `2023`
- Quality indicators: `1080p`, `BluRay`, `WEBRip`
- Codec indicators: `x264`, `H.265`, `HEVC`

### YouTube Detection

YouTube content is identified by:

- Filename contains "youtube" or "yt-dlp"
- Channel name in brackets: `[Channel Name]`
- YouTube video ID patterns

## Database Integration

The system updates the following database tables after successful organization:

- **media_metadata**: Updates `file_path` to new location
- **media**: Updates `file_path` for YouTube content
- **tasks**: Stores organization results in `result_summary`

## Error Handling

### Collision Resolution

When a target file already exists:

1. **Hash Comparison**: If files are identical (same hash), skip the move
2. **Safe Naming**: Generate alternative filename like `Movie (2).mkv`
3. **Timestamp Fallback**: Use timestamp if too many collisions

### Validation

- Source file existence check
- Target directory creation
- Filename sanitization
- Path length validation

## Testing

Run the media organization tests:

```bash
npm test test/media-organizer.test.ts
```

The test suite covers:

- Media type detection
- Filename normalization
- Template formatting
- Organization plan creation
- Dry run execution

## Integration with Existing Systems

### Media Ingestion Pipeline

1. File detected in media directory
2. `media_ingest` task created and executed
3. Metadata extracted and stored
4. `media_organize` task automatically created (if enabled)
5. File organized based on metadata and patterns
6. Database updated with new file paths

### MCP Integration

The organization system integrates with MCP servers for:

- **ChromaDB**: Learning from organization patterns
- **Monitor**: Real-time organization tracking
- **Notifications**: Organization completion alerts

## Future Enhancements

- **Smart Duplicate Detection**: Advanced duplicate handling
- **Custom Rules Engine**: User-defined organization rules
- **Metadata Enrichment**: Fetch additional metadata during organization
- **Undo Functionality**: Ability to reverse organization operations
- **Bulk Operations**: Organize entire directories at once
