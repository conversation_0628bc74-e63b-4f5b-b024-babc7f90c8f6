
# 📄 PRD: Content Discovery and Downloader Integration

## Overview

To support automatic media acquisition, Atlas should integrate with popular content discovery and downloader tools (e.g., Sonarr, Radarr, Prowlarr, qBittorrent, yt-dlp). These integrations will allow the AI orchestrator to request or initiate downloads of missing or newly identified content, syncing them into the processing pipeline.

## Goals

* Enable Atlas to programmatically interface with torrent/NZB indexer managers and media downloaders.
* Create a unified CLI and task interface for requesting new content.
* Integrate downloaded content into the existing media ingestion and analysis pipeline.
* Optionally subscribe to RSS feeds (e.g., YouTube or podcast channels) for automatic downloads.

---

## Milestones and Tasks

### 🧱 Phase 1: Core Downloader Task Integration

#### ✅ 1. Add Downloader Task Type

* **Task Type**: `media_download`
* **Args**:

  ```ts
  {
    source: 'torrent' | 'nzb' | 'youtube' | 'rss',
    url?: string,
    query?: string,
    media_type?: 'movie' | 'tv' | 'music' | 'video',
    destination_path?: string
  }
  ```
* **Behavior**:

  * Dispatches downstream tool commands (e.g., Sonarr API, yt-dlp CLI).
  * Waits for download completion (polling if necessary).
  * On success, enqueues the file into the `media-ingest` pipeline.

#### 🛠️ 2. Implement `download-media.ts` CLI Tool

```bash
bun run src/cli/download-media.ts --source youtube --url https://youtube.com/...
bun run src/cli/download-media.ts --source torrent --query "Movie Title 2024"
```

* Accepts `--source`, `--url`, `--query`, `--media`, and `--dest` flags.
* Supports direct download or task-based download.

#### 🧠 3. Extend Orchestrator to Handle `media_download` Tasks

* Invoke downstream logic based on `source`:

  * `youtube`: run `yt-dlp` via shell
  * `torrent` or `nzb`: hit Sonarr/Radarr/Prowlarr APIs
* On success:

  * Normalize path
  * Dispatch `media-ingest` task for the result
  * Log success/failure to task result

---

### 🔌 Phase 2: Sonarr/Radarr/Prowlarr API Integration

#### 📡 4. Add Integration Module: `services/arr-integrations.ts`

* Use environment variables or config to store:

  ```ts
  {
    sonarr: { baseUrl: string, apiKey: string },
    radarr: { baseUrl: string, apiKey: string },
    prowarr: { baseUrl: string, apiKey: string }
  }
  ```

#### 📥 5. Sonarr/Radarr Request Functions

* `requestShow(showName: string): Promise<void>`
* `checkMissingEpisodes(mediaId: number): Promise<Episode[]>`
* `triggerSearch(showId: number, season: number): Promise<void>`

#### 🔁 6. Media-Aware Download Triggering

* Add detection logic to identify gaps (e.g., missing seasons).
* If media type is TV or Movie:

  * Query existing metadata
  * Trigger Radarr/Sonarr search
  * Log request

---

### 📺 Phase 3: YouTube & RSS Support

#### 📹 7. `youtube-download.ts` Tool

* Accepts a direct URL or channel RSS feed
* Uses `yt-dlp` to download
* Saves file to a monitored directory
* Can be scheduled or event-triggered

#### 📡 8. `rss-watch.ts` Task

* Polls configured RSS feeds (e.g., YouTube, podcasts)
* Detects new episodes
* Enqueues a `media_download` task with `source: 'rss'`

---

### 🧪 Phase 4: Testing & Validation

#### ✅ 9. Implement Tests for Downloader Tasks

* Simulate tasks for:

  * YouTube download
  * Sonarr-triggered download
  * Missing episode check

#### 🔄 10. CLI Command: `--simulate-download` mode

* For testing task flow without real downloads.

---

## Optional Enhancements

* Integration with **qBittorrent** or **NZBGet** directly
* Support **Ombi-like** user request flow (could be AI/CLI-driven)
* Option to prefer magnet links or NZBs
* Fallback to OpenAI to rewrite malformed queries for search

---

## Success Criteria

* CLI tools and task system can request and complete downloads.
* Media appears in the library post-download with correct metadata.
* AI can detect media gaps and resolve them via automated downloads.

