
# Atlas Phase 2: “Advanced AI Features” PRD

**Overview:** Phase 2 extends Atlas’s media pipeline (watch S<PERSON>, transcribe with <PERSON><PERSON><PERSON>, tag via LLM, embed with CLIP/audio models, index in MeiliSearch/ChromaDB) with new AI capabilities. We retain the **CLI-first TypeScript/Bun** architecture and SQLite backend. The goals are to add **content summarization**, **personalized recommendations**, **video scene/object analysis**, and **audio/music classification**. These features enrich the user’s personal media experience (quick overviews, discovery of related content, and deeper understanding of video/audio content).

## Features & Benefits

* **Content Summarization (Media Transcript Summaries):** Automatically generate concise summaries of long transcripts. This lets users grasp content gist without reading all text. For example, a travel vlog transcript can yield a bullet-list summary of key points. Summaries improve accessibility and save time in personal media review.

* **Content Recommendations (Similarity/Personalized Suggestions):** Recommend related media based on content similarity or past user interactions. By treating each media item as a *vector embedding*, Atlas can suggest other content “nearby” in embedding space. E.g. if a user listens to a cooking podcast, Atlas might recommend a similar recipe video. Recommendations leverage existing CLIP/audio embeddings and optional user-preference logs to surface relevant media (akin to “songs that sound similar” in Spotify).

* **Video Scene Detection & Object Recognition:** Automatically segment videos into scenes and label objects. Scene detection finds time boundaries where content changes (e.g. cuts or fades) using tools like FFmpeg’s `scene` filter or PySceneDetect. For instance, FFmpeg can “extract all frames that differ from the previous frame by more than 0.4” using its scene-change filter. Each detected scene is stored (with start/end times). Within scenes, run an object-recognition model (e.g. YOLO via TensorFlow\.js) to label visible objects. YOLO (“You Only Look Once”) is a real-time object detection model family; JS/TS implementations (e.g. YOLO-TFJS-VISION) enable running it on keyframes. The user benefit is searchable video segments (e.g. “skip to where a cat appears”) and richer metadata (tags like “outdoors, car, person”).

* **Audio Classification & Music Analysis:** Analyze audio streams to classify content and extract musical features. For example, detect if a clip is **music vs. speech**, estimate **genre/tempo/key** for songs, or tag ambient sounds. We can integrate audio feature libraries (e.g. [Essentia](https://essentia.upf.edu/) with JS bindings) to compute descriptors and run classification. In practice, this means adding tags like “Jazz, 120 BPM” or “Interview, English speech” to media. The benefit is enhanced searching (find all podcasts, or all ambient tracks) and insights into personal music collection (e.g. tempo, mood detection).

## Technical Implementation

### 1. Content Summarization

**Definition:** Generate a brief summary from a media’s transcript. *User benefit:* Quick overview of video/audio content.

**Architecture & Flow:** After transcription (Whisper) completes, pass the transcript text to a summarizer module. This module uses an LLM or summarization API to produce a summary. (For example, AssemblyAI’s API “generates a summary of the resulting transcript”.) The pipeline flow is:

```
Incoming media → Transcription → [New] Summarization → Save summary → Tagging/Indexing
```

**Modules/Files:** Implement a `Summarizer` service (e.g. `src/summarizer.ts`). This can call an LLM API (OpenAI, Llama, etc.) or a summarization model via a library. It should accept text and return a string summary.
**Function Signature:**

```ts
async function generateSummary(transcriptText: string, style?: string): Promise<string>
```

**CLI:** Add a command `atlas summarize --id <mediaId> [--model <type>]` which reads the transcript of `mediaId`, runs summarization, and outputs/stores the result. It can also allow summarizing arbitrary text. The output format can be plain text or markdown (e.g. bullets).
**DB Changes:** Add a `summary TEXT` column to the `transcripts` table (or a new `summaries` table linked by media ID). After generating the summary, store it in SQLite and update MeiliSearch (so summaries are searchable).
**Integration:** Hook `Summarizer` into the existing processing pipeline. For watched S3 uploads, after `atlas transcribe` writes a transcript, automatically run `generateSummary` and attach it. Also support manual CLI-triggered summarization.

**Tasks/Milestones:**

* *Task:* Define the summary schema (add `summary` field). Update ORM or SQL migration.
* *Task:* Implement `summarizer.ts`, using an LLM/summarization API. (Use async TS, ensure error handling.)
* *Task:* Extend pipeline (e.g. in `processor.ts`) to call `generateSummary(transcript)` after transcription.
* *Task:* Update CLI (`index.ts` or similar) to add `atlas summarize` command with flags.
* *Task:* Write tests: e.g. `Summarizer.generateSummary("Long text...")` yields non-empty summary.
* *Citation:* Modern systems use LLM-based summarization to “distill important information” from transcripts.

### 2. Content Recommendations

**Definition:** Suggest related media items based on content similarity or user history. *User benefit:* Discover new content matching their interests.

**Architecture & Flow:** Utilize existing embeddings (from CLIP for video frames, from audio models for sound) stored in ChromaDB. For similarity-based recommendations, compute nearest neighbors in embedding space. For user-based recommendations, track user interactions (e.g. “played media X at time T”) in a new table, then weight recommendations by user history. Data flow:

```
On new media or user action → (Optional) Update embeddings/user profile → Compute top-N similar items → Return as recommendations
```

**Modules/Files:** Create a `Recommender` module (`src/recommender.ts`). It should have functions like:

```ts
async function findSimilarMedia(mediaId: string, topK: number): Promise<Media[]>; 
async function getUserRecommendations(userId: string, topK: number): Promise<Media[]>;
```

Implement these by querying ChromaDB/MeiliSearch. For example, use ChromaDB to `query({queryEmbeddings: [embeddingOf(mediaId)], nResults: topK})`. See vector search concept. For user-based, combine content similarities from all media the user engaged with.

**CLI:** `atlas recommend --media <mediaId> --top 5` (for item-based recs) and `atlas recommend --user <userId> --top 5`. Output a list of media IDs/titles.
**DB Changes:**

* Add `user_interactions(user_id TEXT, media_id TEXT, action TEXT, timestamp DATETIME)` to log plays/likes/etc.
* (Optional) Precompute a `recommendations(user_id, media_id, score)` table for fast lookups.
  No change needed for embedding storage (ChromaDB holds media embeddings).
  **Integration:** When processing a media file, ensure its embedding vectors are up-to-date in ChromaDB (existing pipeline likely does this already). When a user plays media (if tracking such events), log it. The Recommender module should use these data.

**Tasks/Milestones:**

* *Task:* Define new DB table for user interactions. Write schema migration.
* *Task:* Implement `getUserRecommendations(userId, k)` and `findSimilarMedia(mediaId, k)` using ChromaDB/Meili queries.
* *Task:* Add CLI handler for `atlas recommend`, calling these functions.
* *Task:* (Optional) Develop a simple UI/JSON output with recommended media metadata.
* *Task:* Unit tests: simulate embeddings and verify nearest-neighbor returns sensible items.
* *Citation:* Similarity search uses “vector embeddings” to find related items by “how close their vectors are”, making recommendations more personal.

### 3. Video Scene Detection & Object Recognition

**Definition:** Automatically split videos into scenes and label objects within them. *User benefit:* Quick navigation and deeper insights into video content.

**Scene Detection:** We detect scene boundaries (hard cuts, fades) using a tool or library. For example, FFmpeg’s `select='gt(scene,threshold)'` filter can emit frames on shot changes. We can call FFmpeg via a Bun TS child process or use a Node module. Each detected scene yields a `(startTime, endTime)` pair.
**Object Recognition:** For each scene (or sample frames), run an object-detection model. Use a TypeScript-friendly YOLO implementation (e.g. YOLO-TFJS-VISION). This requires loading a pretrained YOLOv8 model; the `yolo.setup({...})` and `yolo.predict(image)` APIs produce bounding boxes and labels.

**Data Flow:**

```
Video file → (New) detectScenes(videoPath) → [Scene list]  
For each scene:  
    extract keyframe (with ffmpeg) → detectObjects(keyframe) → [Object tags]  
Store scene and object metadata → Index
```

**Modules/Files:**

* `sceneDetector.ts`: implements `detectScenes(videoPath: string): Promise<Scene[]>`. Each `Scene` has `{start: number, end: number, thumbnailPath?}`. Internally it might spawn `ffmpeg -i <video> -filter:v ...`.
* `objectRecognizer.ts`: implements `detectObjects(imagePath: string): Promise<Array<{label: string, confidence: number}>>`, using YOLO.
  **Function Signatures:**

```ts
async function detectScenes(videoPath: string, threshold?: number): Promise<Scene[]>;  
async function detectObjects(imagePath: string): Promise<{label: string, confidence: number}[]>;  
```

**CLI:**

* `atlas detect-scenes --video <file>` (or `--media-id` if already ingested). Writes scene list.
* `atlas detect-objects --scene <sceneId>` or integrated into detect-scenes to auto-label each scene.
  Output JSON: e.g. `[{scene:1, start:"00:00", end:"00:05", objects:["person","dog"]}, ...]`.

**DB Changes:** Add tables:

* `video_scenes(id INTEGER PRIMARY KEY, media_id TEXT, start_ms INTEGER, end_ms INTEGER)`
* `scene_objects(id INTEGER, label TEXT, confidence REAL)` (linked by scene id)
  After detection, insert records into these tables. Also optionally store a thumbnail image path.

**Integration:** Trigger scene detection as a separate CLI command, or as part of processing for video media. Use existing video decoding (ffmpeg must be installed). Object detection can be batched per keyframe. After labeling, update MeiliSearch or ChromaDB metadata with detected objects/tags.

**Tasks/Milestones:**

* *Task:* Research and choose scene-detection approach (FFmpeg vs PySceneDetect). Implement `detectScenes` (FFmpeg example).
* *Task:* Create `Scene` database schema and ingestion code.
* *Task:* Integrate a YOLOv8 model via TFJS: add `yolo-tfjs-vision` as dependency. Implement `detectObjects`.
* *Task:* CLI integration: `atlas detect-scenes` triggers both scene splits and object labeling.
* *Task:* Write unit tests: supply short sample video, verify scene timestamps and some object labels.
* *Citation:* FFmpeg can detect scene cuts using `-filter:v "select='gt(scene,0.4)'"`, extracting frames where adjacent frames differ significantly. YOLO-based models enable “real-time object detection” on video frames.

### 4. Audio Classification & Music Analysis

**Definition:** Classify and analyze audio content. *User benefit:* Automatic tagging of music/speech and extraction of musical metadata enriches the library (e.g. filter by genre, mood, language).

**Implementation:** Use audio analysis libraries or ML models. Essentia (with JS binding) offers algorithms for features (tempo, key, etc.). We can either embed Essentia via a Node add-on or call a Python script. Also, incorporate pretrained audio classifiers (e.g. a CNN to label “music genre” or “environmental sound”).

**Data Flow:**

```
Audio track (or extracted from video) → (New) analyzeAudio(file) → {isMusic:boolean, genre?:string, tempo?:number, key?:string, speechLang?:string, ...}  
Save features → Tag/Index.
```

**Modules/Files:** `audioAnalyzer.ts`: implements `analyzeAudio(mediaId: string): Promise<AudioFeatures>`. Use e.g.:

```ts
interface AudioFeatures {
  isMusic: boolean;
  genre?: string;
  bpm?: number;
  key?: string;
  mood?: string;
  [extra: string]: any;
}
```

**CLI:** `atlas audio-analyze --media <id>`. Prints classification and features.

**DB Changes:** Add `audio_features` table with columns `(media_id TEXT PRIMARY KEY, is_music BOOLEAN, genre TEXT, bpm REAL, key TEXT, ...)`. Also could add `tags` linking table for multiple genre labels.

**Integration:** After transcription or video processing, extract the audio track (if not already separate) and call `analyzeAudio`. Use results to add tags/embeddings. Example: if genre “Jazz” is detected, add “genre\:JAZZ” to the media’s tag list (LLM or manual).

**Tasks/Milestones:**

* *Task:* Integrate Essentia (via npm or spawn). Implement feature extraction (tempo, key, etc.) using Essentia API.
* *Task:* (Optional) Integrate a music-genre classifier model (e.g. TensorFlow or pretrained network).
* *Task:* Define `audio_features` schema and write ingestion code.
* *Task:* Extend CLI (`atlas audio-analyze`) and processing pipeline to call `analyzeAudio` on new media.
* *Task:* Unit tests: analyze sample MP3, verify features (e.g. known BPM, genre).
* *Citation:* Essentia is an “open-source library for audio analysis and music information retrieval” with a large collection of algorithms. Audio classification aims to tag audio signals (e.g. music vs speech) into categories.

## Milestones & Roadmap

The implementation should be broken into iterative milestones. For example:

| Milestone              | Features             | Key Tasks                                                        |
| ---------------------- | -------------------- | ---------------------------------------------------------------- |
| 1. Summarization POC   | Summarization        | Schema update, implement summarizer module, CLI, index summary.  |
| 2. Recommendation Core | Recommendations      | Track interactions, embed extraction, rec engine functions, CLI. |
| 3. Video Analysis      | Scene & Object       | Scene detector, DB schema, YOLO integration, CLI commands.       |
| 4. Audio Analysis      | Audio Classification | Essentia integration, feature DB, CLI, tagging.                  |
| 5. Testing & Polish    | All                  | End-to-end tests, performance tuning, documentation.             |

Each milestone contains modular sub-tasks as outlined above. Tasks should create clear, testable TypeScript modules, with function signatures and CLI hooks. Existing architecture is reused (e.g. ChromaDB client, MeiliSearch indexer) wherever possible. At each stage, update SQLite schema, provide migration scripts, and extend the CLI help.

**Example CLI Summary:**

```bash
$ atlas summarize --media 123       # Generate summary for media ID 123
$ atlas recommend --user alice      # Show recommended media for user 'alice'
$ atlas detect-scenes --video clip.mp4
$ atlas detect-objects --scene 456  # (if separate command)
$ atlas audio-analyze --media 789
```

These commands produce structured output (JSON or formatted text) and populate the database.

**Integration Points:**

* **Data Flow:** Summarizer uses transcript table → adds summary. Recommender uses embeddings (ChromaDB) and optional `user_interactions` table. Scene/Object modules feed into `video_scenes`/`scene_objects` tables. Audio analyzer feeds into `audio_features` table.
* **Modules:** Place new code under `src/` following existing style. Use async/await, TS types, and Bun’s bundle system.
* **Databases:** SQLite migrations for new tables/columns. Use the same ORM or raw SQL as project. Index new fields in MeiliSearch if needed (e.g. summary text, object tags).
* **Dependencies:** New dependencies may include TensorFlow\.js (for YOLO), Essentia or other audio libs, and any LLM client library. Ensure to update `package.json` and test installation.

