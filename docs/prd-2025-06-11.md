
# 📄 PRD: Media Ingestion & Metadata Extraction (Phase 4)

### Objective

Build an always-on component that automatically detects new media files and extracts rich metadata to enable future AI-enhanced categorization, transcription, and semantic search. This phase establishes the foundation for the autonomous media intelligence system described in the research document.

---

## ✅ Goals

* **Automatic Media Detection**: Watch designated media folders and automatically create ingestion tasks for new files
* **Rich Metadata Extraction**: Extract comprehensive technical and embedded metadata using industry-standard tools
* **Seamless Integration**: Integrate with existing task orchestration, MCP learning system, and retry mechanisms
* **Deduplication**: Prevent reprocessing of identical files using content-based hashing
* **Extensible Foundation**: Enable downstream tasks (transcription, AI tagging, organization) through metadata-aware planning
* **Production Ready**: Include comprehensive error handling, logging, and monitoring integration

---

## 🧠 Functional Requirements

### 📂 Media File Detection Strategy

**Current System**: File watcher monitors `incoming/` for JSON/MD task files
**New Requirement**: Extend to detect media files in `media/` folder without interfering with task file processing

* [ ] **Dual-Path File Watcher**: Extend existing file watcher to monitor both `incoming/` (task files) and `media/` (media files)
* [ ] **Media File Extensions**: Support comprehensive list of media formats:
  * **Video**: `.mp4`, `.mkv`, `.avi`, `.mov`, `.wmv`, `.flv`, `.webm`, `.m4v`, `.3gp`
  * **Audio**: `.mp3`, `.flac`, `.wav`, `.aac`, `.ogg`, `.m4a`, `.wma`, `.opus`
* [ ] **Automatic Task Creation**: Generate `media_ingest` tasks automatically when media files are detected
* [ ] **File Path Handling**: Support nested directory structures within `media/` folder

### 🛠 Metadata Extraction Engine

**Integration Point**: New executor following existing pattern (similar to `youtube.ts`, `shell.ts`)

* [ ] **New Executor**: `src/executors/media.ts` with `executeMediaIngestTask(task: MediaIngestTask)`
* [ ] **Tool Selection**: Use `ffprobe` (part of FFmpeg) as primary tool for cross-platform compatibility
* [ ] **Fallback Strategy**: Implement `mediainfo` as fallback if `ffprobe` fails
* [ ] **Metadata Schema**: Extract standardized metadata structure:

```typescript
interface MediaMetadata {
  // File Information
  filename: string;
  filepath: string;
  filesize: number;
  file_hash: string;

  // Technical Metadata
  format: string;           // Container format (mp4, mkv, etc.)
  duration: number;         // Duration in seconds
  bitrate: number;          // Overall bitrate

  // Video Streams (if present)
  video?: {
    codec: string;          // H.264, H.265, VP9, etc.
    resolution: string;     // "1920x1080"
    width: number;
    height: number;
    fps: number;
    bitrate: number;
  }[];

  // Audio Streams
  audio?: {
    codec: string;          // AAC, MP3, FLAC, etc.
    channels: number;       // 2, 5.1, 7.1, etc.
    sample_rate: number;    // 44100, 48000, etc.
    bitrate: number;
    language?: string;      // ISO language code
  }[];

  // Subtitle Streams
  subtitles?: {
    codec: string;          // SRT, ASS, VTT, etc.
    language?: string;      // ISO language code
    forced: boolean;
  }[];

  // Embedded Metadata
  title?: string;
  artist?: string;
  album?: string;
  year?: number;
  genre?: string;
  description?: string;

  // Content Analysis (Phase 4 foundation)
  guessed_type?: 'movie' | 'tv_episode' | 'music' | 'podcast' | 'other';
  confidence?: number;      // 0-1 confidence in guessed_type
}
```

### 🧱 Database Integration

**Current System**: Existing `media` table used by YouTube executor, `tasks` table for all task types
**Integration Strategy**: Extend existing schema rather than create new tables

* [ ] **New Task Type**: Add `media_ingest` to existing task type system
* [ ] **Media Metadata Table**: Create `media_metadata` table linked to tasks:

```sql
CREATE TABLE media_metadata (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    file_path TEXT NOT NULL,
    file_hash TEXT NOT NULL UNIQUE,
    metadata_json TEXT NOT NULL,  -- Full MediaMetadata as JSON
    extracted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    tool_used TEXT NOT NULL,      -- 'ffprobe' or 'mediainfo'
    FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE
);
```

* [ ] **Task Result Integration**: Store summary in `tasks.result_summary` for dashboard display
* [ ] **Deduplication**: Use `file_hash` to prevent reprocessing identical files
* [ ] **Index Creation**: Add indexes for common queries (file_hash, file_path, extracted_at)

### 📓 Enhanced Logging & Monitoring

**Current System**: Structured logging with logger utility, MCP monitoring integration
**Enhancement**: Extend existing patterns for media-specific logging

* [ ] **Structured Logging**: Log all media ingestion events with consistent format
* [ ] **Raw Tool Output**: Store complete `ffprobe`/`mediainfo` output in task logs
* [ ] **Error Categorization**: Classify failures (corrupted file, unsupported format, tool error)
* [ ] **MCP Integration**: Send media ingestion events to monitoring MCP server
* [ ] **Performance Metrics**: Track extraction time, file sizes, success rates

### 🔁 Deduplication & Retry Logic

**Current System**: File hash calculation in `utils/hash.ts`, retry system in `retry/`
**Integration**: Leverage existing systems with media-specific enhancements

* [ ] **Content-Based Deduplication**: Use SHA256 hash to identify identical files regardless of filename
* [ ] **Retry Policy**: Create media-specific retry policy for transient failures
* [ ] **Force Reprocessing**: Support `force: true` flag in task metadata to bypass deduplication
* [ ] **Partial Failure Handling**: Retry metadata extraction with different tools if primary fails

---

## 🔗 System Integration Points

### 🤖 MCP Integration (ChromaDB & Monitoring)

**Current System**: Enhanced task processor with ChromaDB embeddings and WebSocket monitoring
**Integration**: Extend existing MCP capabilities for media intelligence

* [ ] **ChromaDB Embeddings**: Store media metadata embeddings for similarity search
* [ ] **Task Learning**: Learn from successful/failed media ingestion patterns
* [ ] **Monitoring Integration**: Send real-time media ingestion events to monitoring MCP
* [ ] **Pattern Recognition**: Identify common media types and processing patterns

### 🔄 Task Orchestration Integration

**Current System**: 5-second orchestrator loop, dependency management, parent-child task relationships
**Integration**: Seamless integration with existing task flow

* [ ] **Automatic Task Creation**: Media files automatically generate tasks without manual intervention
* [ ] **Dependency Support**: Support dependencies for complex media processing workflows
* [ ] **Batch Processing**: Support batch ingestion of multiple media files
* [ ] **Parent Task Completion**: Properly handle parent task completion when all media files processed

### 🔁 Retry System Integration

**Current System**: Configurable retry policies per task type, backoff strategies
**Media-Specific Requirements**: Handle media-specific failure modes

* [ ] **Media Retry Policy**: Create specific retry policy for media ingestion tasks
* [ ] **Tool Fallback**: Retry with different tools (ffprobe → mediainfo) on failure
* [ ] **Transient Error Handling**: Retry on file lock, temporary I/O errors
* [ ] **Non-Retryable Errors**: Don't retry on corrupted files, unsupported formats

### 📊 Dashboard Integration

**Current System**: Auto-generated dashboard showing task status and results
**Enhancement**: Media-specific dashboard elements

* [ ] **Media Statistics**: Show ingestion success rates, file types processed
* [ ] **Metadata Preview**: Display key metadata in dashboard task view
* [ ] **Error Analysis**: Categorize and display media-specific errors

---

## 📐 Technical Architecture

### File Flow Diagram

```
media/
├── movies/
│   └── The.Matrix.1999.mkv ──┐
├── tv/                       │
│   └── Show.S01E01.mp4 ──────┼─▶ [File Watcher]
└── music/                    │     │
    └── song.flac ────────────┘     │
                                     ▼
                              [Media File Detector]
                                     │
                                     ▼
                              [Create media_ingest Task]
                                     │
                                     ▼
                              [Task Orchestrator]
                                     │
                                     ▼
                              [executeMediaIngestTask()]
                                     │
                              ┌──────┴──────┐
                              ▼             ▼
                         [ffprobe]    [mediainfo]
                              │             │
                              ▼             ▼
                         [Parse JSON]  [Parse Text]
                              │             │
                              └──────┬──────┘
                                     ▼
                              [Normalize Metadata]
                                     │
                              ┌──────┴──────┐
                              ▼             ▼
                         [Store in DB]  [MCP Learning]
                              │             │
                              ▼             ▼
                         [Update Task]  [Pattern Analysis]
                              │             │
                              └──────┬──────┘
                                     ▼
                              [Task Completed]
                                     │
                                     ▼
                              [Trigger Downstream Tasks?]
```

### Task Type Definition

```typescript
export interface MediaIngestTask extends BaseTaskFields {
    type: 'media_ingest';
    description: string;        // Auto-generated: "Ingest metadata for {filename}"
    file_path: string;         // Full path to media file
    force?: boolean;           // Skip deduplication if true
    tool_preference?: 'ffprobe' | 'mediainfo' | 'auto';
}
```

---

## 🧪 Comprehensive Test Plan

### Unit Tests

* [ ] **Executor Tests**: Test `executeMediaIngestTask()` with various media formats
* [ ] **Metadata Parsing**: Validate JSON schema structure for different file types
* [ ] **Error Handling**: Test behavior with corrupted files, missing tools
* [ ] **Deduplication**: Verify hash-based duplicate detection
* [ ] **Tool Fallback**: Test ffprobe → mediainfo fallback logic

### Integration Tests

* [ ] **File Watcher**: Drop files into `media/` and verify task creation
* [ ] **Database Integration**: Verify metadata storage and retrieval
* [ ] **MCP Integration**: Test ChromaDB embedding storage and monitoring events
* [ ] **Retry Logic**: Test retry behavior with transient failures
* [ ] **Dashboard Display**: Verify media tasks appear correctly in dashboard

### End-to-End Tests

* [ ] **Complete Workflow**: File drop → detection → ingestion → storage → completion
* [ ] **Batch Processing**: Multiple files dropped simultaneously
* [ ] **Error Recovery**: System behavior during tool failures
* [ ] **Performance**: Large file handling and processing time

### Media Format Coverage

* [ ] **Video Formats**: MP4, MKV, AVI, MOV, WebM
* [ ] **Audio Formats**: MP3, FLAC, WAV, AAC, OGG
* [ ] **Edge Cases**: Zero-byte files, permission issues, network files
* [ ] **Metadata Variations**: Files with/without embedded metadata

---

## 🧩 Implementation Checklist

### Core Implementation

* [ ] **Task Type System**:
  - [ ] Add `media_ingest` to `TASK_TYPES` in `src/types/task.ts`
  - [ ] Create `MediaIngestTask` interface
  - [ ] Update `BaseTask` discriminated union

* [ ] **Executor Implementation**:
  - [ ] Create `src/executors/media.ts`
  - [ ] Implement `executeMediaIngestTask()` function
  - [ ] Add to `taskExecutors` map in `dispatcher.ts`
  - [ ] Update `executeTask()` switch statement

* [ ] **File Watcher Extension**:
  - [ ] Modify `src/index.ts` file watcher logic
  - [ ] Add media file detection function
  - [ ] Implement automatic task creation for media files

* [ ] **Database Schema**:
  - [ ] Add `media_metadata` table creation to `src/db.ts`
  - [ ] Create database migration if needed
  - [ ] Add indexes for performance

### Validation & CLI

* [ ] **Task Validation**:
  - [ ] Add `validateMediaIngestTask()` to `src/validation/schemas.ts`
  - [ ] Update `validateTask()` function
  - [ ] Add to `TaskValidators` mapping

* [ ] **CLI Integration**:
  - [ ] Update `src/cli/lint-task.ts` to support media_ingest tasks
  - [ ] Add auto-fix rules for media tasks
  - [ ] Update help text and examples

### Testing & Documentation

* [ ] **Test Coverage**:
  - [ ] Add media executor tests to `test/executors.test.ts`
  - [ ] Create `test/media-ingestion.test.ts` for integration tests
  - [ ] Update existing test suites

* [ ] **Documentation**:
  - [ ] Create example media_ingest task files in `docs/examples/`
  - [ ] Update system diagram if needed
  - [ ] Add media ingestion section to README

### Configuration & Monitoring

* [ ] **Configuration Extensions**:
  - [ ] Add `media` section to config with tool paths and preferences
  - [ ] Configure supported file extensions list
  - [ ] Set extraction timeout limits
  - [ ] Configure deduplication behavior

```typescript
// Addition to src/config.ts
export const config = {
  // ... existing config
  media: {
    tools: {
      ffprobe: process.env.FFPROBE_PATH || 'ffprobe',
      mediainfo: process.env.MEDIAINFO_PATH || 'mediainfo',
      preferred: 'ffprobe' as 'ffprobe' | 'mediainfo' | 'auto'
    },
    extensions: {
      video: ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp'],
      audio: ['.mp3', '.flac', '.wav', '.aac', '.ogg', '.m4a', '.wma', '.opus']
    },
    extraction: {
      timeout_ms: 30000,
      max_file_size_mb: 10000,
      enable_deduplication: true
    }
  }
};
```

* [ ] **MCP Integration**:
  - [ ] Extend ChromaDB integration for media metadata embeddings
  - [ ] Add monitoring events for media ingestion pipeline
  - [ ] Configure embedding generation for semantic media search
  - [ ] Set up pattern recognition for media processing optimization

---

## 📋 Dependencies & Prerequisites

### External Tools Required
* [ ] **FFmpeg/FFprobe**: Primary metadata extraction tool
  - Installation: `apt install ffmpeg` (Linux) or `brew install ffmpeg` (macOS)
  - Windows: Download from https://ffmpeg.org/download.html
  - Verify: `ffprobe -version`

* [ ] **MediaInfo** (Optional fallback):
  - Installation: `apt install mediainfo` (Linux) or `brew install mediainfo` (macOS)
  - Windows: Download from https://mediaarea.net/en/MediaInfo
  - Verify: `mediainfo --version`

### System Requirements
* [ ] **Disk Space**: Adequate space for media files and metadata storage
* [ ] **Performance**: Consider CPU usage for large media files
* [ ] **Network**: If processing network-mounted media files

---

## 🚀 Future Phase Integration

### Phase 5: Transcription & Text Analysis
* Media metadata enables automatic transcription task creation
* Audio/video files with speech detected → transcription tasks
* Metadata provides duration estimates for transcription planning

### Phase 6: AI-Enhanced Tagging
* Rich metadata enables content-aware AI tagging
* Video resolution/codec → quality-based categorization
* Audio channels/bitrate → content type inference

### Phase 7: Autonomous Organization
* Metadata-driven file organization and renaming
* Content type detection → automatic folder structure
* Duplicate detection → consolidation recommendations

This Phase 4 implementation provides the essential foundation for all subsequent autonomous media intelligence capabilities.

---

## 🛣️ Implementation Roadmap

### Phase 4.1: Core Infrastructure (Week 1)
1. **Task Type System**: Add `media_ingest` task type and validation
2. **Database Schema**: Create `media_metadata` table and indexes
3. **Basic Executor**: Implement `executeMediaIngestTask()` with ffprobe
4. **File Detection**: Extend file watcher for media files

### Phase 4.2: Metadata Processing (Week 2)
1. **Metadata Extraction**: Complete ffprobe integration and JSON parsing
2. **Tool Fallback**: Add mediainfo fallback support
3. **Error Handling**: Implement comprehensive error categorization
4. **Deduplication**: Add hash-based duplicate detection

### Phase 4.3: System Integration (Week 3)
1. **MCP Integration**: Add ChromaDB and monitoring support
2. **Retry Logic**: Implement media-specific retry policies
3. **Dashboard**: Add media ingestion display to dashboard
4. **CLI Support**: Add lint-task validation for media tasks

### Phase 4.4: Testing & Polish (Week 4)
1. **Test Coverage**: Complete unit and integration test suite
2. **Performance**: Optimize for large files and batch processing
3. **Documentation**: Finalize examples and documentation
4. **Deployment**: Production readiness and monitoring

### Success Criteria
- [ ] Media files automatically detected and processed
- [ ] Rich metadata extracted and stored consistently
- [ ] Zero manual intervention required for standard workflows
- [ ] Comprehensive error handling and recovery
- [ ] Full integration with existing MCP and monitoring systems
- [ ] Foundation ready for Phase 5 transcription integration

---

## 📚 Related Documentation

* **Research Document**: `docs/research-2025-06-11.md` - Overall system vision and roadmap
* **System Diagram**: `docs/system-diagram.mmd` - Current system architecture
* **Example Tasks**: `docs/examples/` - Sample media ingestion task files
* **Current Codebase**: Existing task orchestration and MCP integration patterns

This PRD aligns Phase 4 implementation with your existing sophisticated task orchestration system while establishing the foundation for the autonomous media intelligence capabilities outlined in your research document.

