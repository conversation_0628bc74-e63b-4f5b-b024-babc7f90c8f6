
# 📄 PRD: Automatic Organization of Media Files

### Objective

Automatically rename and move ingested media files into a clean, consistent folder structure based on extracted metadata (e.g., title, year, media type). This improves browseability, supports Plex/Jellyfin-style libraries, and sets the stage for tagging and search.

---

## ✅ Goals

* Normalize file/folder naming conventions.
* Organize media into subdirectories (e.g. by type, year, title).
* Avoid filename collisions and ensure idempotency.
* Integrate into the task system as a follow-up to `media_ingest`.
* Maintain traceability via task log and database updates.

---

## 🧠 Functional Requirements

### 🧾 Triggering

* [ ] New task type: `media_organize`
* [ ] Created automatically after successful `media_ingest`, unless explicitly disabled via config or plan file.
* [ ] Manual triggering supported via CLI or `.plan.json`.
* [ ] Batch organization support for multiple files (e.g., entire series or folder).

### 📂 Destination Structure

Default folder layout should use the existing collection paths defined in config:

```plaintext
/media-library/
  ├── Movies/                  # config.media.collectionMovies
  │   └── The Matrix (1999)/
  │       └── The Matrix (1999).mkv
  ├── Shows/                   # config.media.collectionTv
  │   └── The Simpsons/
  │       └── Season 05/
  │           └── S05E02 - Cape Feare.mkv
  ├── YouTube/                 # config.media.collectionYouTube
  │   └── Channel Name/
  │       └── Video Title.mp4
  └── Downloads (temp area)/   # config.media.collectionCatchAll
      └── Uncategorized Media.mp3
```

Configurable structure example:

```ts
media.organize = {
  enabled: true,
  categorization: {
    // Rules for determining media type (TV, Movie, YouTube, etc.)
    useMetadataType: true,     // Use metadata to determine type when available
    fallbackToFilename: true,  // Use filename patterns as fallback
    defaultCategory: 'catchall' // Where to put media if type can't be determined
  },
  folderStructure: {
    movies: {
      pattern: "{title} ({year})",
      groupByYear: false,      // Whether to add /2023/ parent folders
      groupByGenre: false      // Whether to add /Action/ parent folders
    },
    tv: {
      pattern: "{series}/Season {season:02d}/{series} - S{season:02d}E{episode:02d} - {title}",
      groupBySeries: true      // Always group by series name
    },
    youtube: {
      pattern: "{channel}/{title}",
      groupByChannel: true     // Group by YouTube channel
    }
  },
  filenameNormalization: {
    maxLength: 180,            // Max filename length
    case: "title",             // title, lower, upper, etc.
    replaceSpaces: false,      // Replace spaces with dots or underscores
    sanitizeChars: true        // Remove illegal characters
  }
}
```

### 🏗 Filename Normalization

* [ ] Sanitized (no special characters, no trailing dots).
* [ ] Truncated or padded to avoid OS path limits.
* [ ] Ensure consistent case (PascalCase recommended).

### 🧱 DB Updates

* [ ] Update `media_metadata.file_path` to new location after move.
* [ ] Update `media.file_path` for YouTube content when applicable.
* [ ] Update `tasks.result_summary` for the organizing task with before/after paths.
* [ ] Mark `media_organize` task as `completed`, `skipped`, or `failed`.

---

## 🔄 Retry / Idempotency

* [ ] If the destination file already exists and matches hash → skip.
* [ ] If destination differs, auto-resolve:

  * Rename as `filename (copy 2).mkv`
  * Or append short hash to name
* [ ] Task can be safely retried without duplicating moves.

---

## 📓 Logging

* [ ] Log original path, target path, rename strategy used.
* [ ] Log any skipped moves due to hash match.
* [ ] Include organizing results in audit log.

---

## 🧩 Integration Checklist

| Area       | Requirement                                        | Status |
| ---------- | -------------------------------------------------- | ------ |
| Tasks      | `media_organize` type + automatic creation         | \[ ]   |
| Executor   | `executeMediaOrganizeTask()`                       | \[ ]   |
| DB         | Update `media_metadata.file_path` and `media.file_path` | \[ ]   |
| Logging    | Log rename/move logic                              | \[ ]   |
| Validation | `lint-task` support + filename sanity rules        | \[ ]   |
| Config     | Add `media.organize` section                       | \[ ]   |
| CLI        | `bun run organize-media <hash>` for manual trigger | \[ ]   |
| Batch      | Support for organizing multiple files at once      | \[ ]   |

---

## 🧪 Test Plan

### Unit

* [ ] File renamer given `MediaMetadata` produces valid names.
* [ ] Collision resolver picks safe filenames.

### Integration

* [ ] File is renamed and moved correctly.
* [ ] DB reflects new path.
* [ ] Task succeeds or fails gracefully.
* [ ] Re-run is idempotent (no dupes).

---

## 🚦 Future Integration

This organization system lays the groundwork for:

* 🎞️ Plex/Jellyfin compatibility
* 🏷️ Smart tagging based on file path or folder names
* 📼 Visual dashboards for type/category breakdowns
* 🧠 Training classifier models on organized content

## 🔌 Integration with Existing Config

The organization system will leverage the existing collection paths defined in `config.ts`:

```typescript
// Existing config structure to integrate with
media: {
    collectionPath: process.env.MEDIA_COLLECTION_PATH || 'E:\\',
    collectionTv: process.env.MEDIA_COLLECTION_TV || `${MEDIA_COLLECTION_PATH}/Shows`,
    collectionMovies: process.env.MEDIA_COLLECTION_MOVIES || `${MEDIA_COLLECTION_PATH}/Movies`,
    collectionYouTube: process.env.MEDIA_COLLECTION_YOUTUBE || `${MEDIA_COLLECTION_PATH}/YouTube`,
    collectionCatchAll: process.env.MEDIA_COLLECTION_CATCHALL || `${MEDIA_COLLECTION_PATH}/Downloads (temp area)`,
    
    // New organize section to add
    organize: {
        enabled: true,
        categorization: {
            useMetadataType: true,
            fallbackToFilename: true,
            defaultCategory: 'catchall'
        },
        // Additional organization settings...
    }
}
```

### 🧩 Media Type Detection

The system will determine the appropriate collection folder based on:

1. **Metadata Analysis**: Using extracted metadata to identify TV shows, movies, etc.
2. **Filename Patterns**: Fallback to analyzing filenames (e.g., S01E01 pattern for TV shows)
3. **Source Context**: Consider the task that created the media (e.g., YouTube downloads)
4. **Default Category**: Use `collectionCatchAll` if type cannot be determined

## 💻 Implementation Details

### Task Type Definition

```typescript
export interface MediaOrganizeTask extends BaseTaskFields {
    type: 'media_organize';
    description: string;        // Auto-generated: "Organize media files for {filename}"
    file_path?: string;         // Single file path (optional if batch)
    file_paths?: string[];      // Multiple file paths for batch organization
    target_collection?: 'tv' | 'movies' | 'youtube' | 'catchall'; // Force specific collection
    force?: boolean;            // Override existing files if true
    dry_run?: boolean;          // Simulate organization without moving files
    metadata?: {                // Optional metadata to use instead of looking up
        [key: string]: any;     // Can include title, year, series, etc.
    };
}
```

### Executor Implementation

The `executeMediaOrganizeTask` function will:

1. Determine the appropriate collection for each file
2. Generate the target path based on metadata and configuration
3. Create necessary directories
4. Move the file to its new location
5. Update database records
6. Return success/failure with detailed results

For batch operations, the executor will process each file individually but within a single transaction to ensure database consistency.

