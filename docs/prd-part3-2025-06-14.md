
# 📄 PRD: Autonomous Learning and Optimization

## Overview

This PRD describes how Atlas can gradually become smarter over time through self-analysis, user feedback, and vector-based reasoning. The system will log metadata, task outcomes, and user actions, then analyze this data to optimize tagging, task scheduling, and recommendations.

---

## Goals

* Introduce mechanisms for Atlas to learn from:

  * Media similarity (via embeddings + vector search)
  * Failed tasks and runtime metrics
  * User interventions (manual tag edits, file moves)
* Build infrastructure for:

  * Semantic search & clustering of media
  * Log-based pattern recognition
  * Optional LLM-assisted planning
* Use ChromaDB for vector similarity search.
* Lay groundwork for feedback-aware and self-optimizing behaviors.

---

## Architecture Components

* ✅ ChromaDB (vector database) – already part of the project
* ✅ Logs & metadata database – already used by task system
* ✅ CLI-based tooling and Bun runtime – existing convention
* ⏳ New modules needed:

  * `embedding-service.ts`
  * `analytics/logger.ts`
  * `feedback-tracker.ts`
  * `autolearn-agent.ts` (future)

---

## Milestones and Tasks

### 📐 Phase 1: Vector Embedding for Media and Tasks

#### ✅ 1. Extract Media Embeddings

* CLI tool: `bun run src/cli/atlas-embed-media.ts --media 123`
* Store vector embeddings of:

  * Transcripts (text)
  * Audio/mood/genre tags
  * Descriptions/summaries
* Embed using OpenAI or local model
* Store in ChromaDB:

  ```ts
  {
    id: mediaId.toString(),
    metadata: { title, tags, genre, summary, ... },
    embedding: [vector...]
  }
  ```

#### 🧠 2. Vector Search CLI

```bash
bun run src/cli/atlas-search-similar.ts --media 456 --top 10
```

* Finds similar media by vector proximity
* Used for:

  * Recommendations
  * “Group similar” views
  * Clustering

---

### 📊 Phase 2: Logging and Pattern Tracking

#### 🛠 3. Add Task Metrics Logging

* Extend task execution results to include:

  * Start/end timestamp
  * Duration
  * Success/failure reason
  * Retry count
* Store in `task_logs` table:

  ```ts
  {
    task_id, type, duration_ms, status, retries, created_at
  }
  ```

#### 📈 4. CLI Tool: `analyze-task-metrics.ts`

* Query task logs
* Output:

  * Most common failures
  * Average duration by task type
  * Trends in failures over time

#### 🧪 5. Detect Bottlenecks

* Add logic to monitor:

  * Tasks exceeding a runtime threshold
  * Orchestration backlogs
  * Frequent retry loops

---

### 🪄 Phase 3: Feedback Awareness

#### 🧾 6. Track User Corrections

* When tags are edited via CLI/UI, store:

  ```ts
  {
    media_id,
    original_tags: [...],
    corrected_tags: [...],
    timestamp,
    source: 'user'
  }
  ```
* CLI tool: `track-tag-edits.ts --media 123 --tags "Action, Sci-fi"`

#### 🔄 7. Feedback Loop Learner

* Daily task: analyze tag correction patterns
* Learn heuristic rules (e.g., “If genre contains ‘robot’, prefer Sci-fi”)
* Apply via task preprocessor module

---

### 🧭 Phase 4: Planning and Recommendation (Stretch)

#### 🧠 8. LLM-Based Planner (optional)

* Task: `media_plan_recommendation`
* Input: logs + metadata snapshot
* Prompt: “Suggest optimizations for common failures or missing metadata”
* Output examples:

  * “Movie X has no subtitles – schedule subtitle download”
  * “Podcast feed has 10 new entries – fetch with RSS downloader”

#### 🗂 9. Rule Scheduler

* Detect periodic patterns:

  * “Show Y receives 10 episodes every Friday”
* Pre-schedule Sonarr searches accordingly

---

## CLI Tools to Add

| Tool                      | Purpose                                 |
| ------------------------- | --------------------------------------- |
| `atlas-embed-media.ts`    | Generate & store vector embeddings      |
| `atlas-search-similar.ts` | Find similar media via ChromaDB         |
| `analyze-task-metrics.ts` | Print common task failures or slowdowns |
| `track-tag-edits.ts`      | Record tag changes for training         |
| `run-feedback-loop.ts`    | Learn heuristics from user behavior     |

---

## Future Considerations

* Use Chroma to power personalized media suggestions.
* Implement semantic clustering views (e.g., “group by mood”).
* Add UI dashboard for learning stats and optimization tips.
* Explore online fine-tuning or few-shot rule engine adaptations.

---

## Success Criteria

* Media is clustered and searchable by semantic meaning.
* The system can report which tasks fail most often and why.
* User edits influence future tag suggestions.
* (Optional) LLM planner recommends corrective tasks.
* Logs and patterns are queryable for optimization insights.
