{"id": "phase3-planner-demo", "type": "planner", "description": "Create a comprehensive Python data analysis pipeline that reads CSV data, performs statistical analysis, and generates visualizations", "status": "pending", "metadata": {"created_by": "phase3-demo", "priority": 1, "tags": ["demo", "phase3", "planner", "data-analysis", "python"], "assertions": [{"id": "plan_contains_subtasks", "type": "output_contains", "description": "Plan should contain multiple subtasks", "condition": "subtasks:", "severity": "error", "message": "The generated plan must include a subtasks section"}, {"id": "plan_includes_review", "type": "output_contains", "description": "Plan should include review tasks", "condition": "type: review", "severity": "warning", "message": "Consider including review tasks for quality assurance"}, {"id": "plan_includes_code_generation", "type": "output_contains", "description": "Plan should include code generation", "condition": "type: code", "severity": "error", "message": "Data analysis pipeline requires code generation tasks"}]}}