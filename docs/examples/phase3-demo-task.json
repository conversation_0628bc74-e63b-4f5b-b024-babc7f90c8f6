{"id": "phase3-demo", "type": "code", "description": "Generate a Python script that calculates fibon<PERSON>ci numbers with error handling", "status": "pending", "metadata": {"created_by": "phase3-demo", "priority": 1, "tags": ["demo", "phase3", "<PERSON><PERSON><PERSON><PERSON>", "python"], "assertions": [{"id": "output_contains_function", "type": "output_contains", "description": "Output should contain a <PERSON><PERSON><PERSON><PERSON> function definition", "condition": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "severity": "error", "message": "The generated code must include a <PERSON><PERSON><PERSON><PERSON> function definition"}, {"id": "output_contains_error_handling", "type": "output_contains", "description": "Output should include error handling", "condition": "try:", "severity": "warning", "message": "Consider adding error handling with try-except blocks"}, {"id": "output_contains_docstring", "type": "output_contains", "description": "Function should have documentation", "condition": "\"\"\"", "severity": "info", "message": "Functions should include docstrings for better documentation"}, {"id": "file_created", "type": "file_exists", "description": "Python file should be created", "condition": "./outputs/fibonacci.py", "severity": "error", "message": "The fibonacci.py file should be created in the outputs directory"}, {"id": "valid_python_syntax", "type": "custom_script", "description": "Generated code should have valid Python syntax", "condition": "python -m py_compile ./outputs/fibonacci.py", "severity": "error", "message": "The generated Python code must have valid syntax"}]}}