---
id: media-workflow-001
type: media_ingest
description: Ingest metadata for podcast episode with downstream transcription
file_path: C:/Users/<USER>/Sync/AI Drop Box/media/podcasts/tech-talk-ep-42.mp3
tool_preference: ffprobe
dependents:
  - transcribe-tech-talk-ep-42
  - ai-tag-tech-talk-ep-42
metadata:
  created_by: planner
  priority: 1
  tags:
    - media
    - podcast
    - audio
    - transcription_ready
  downstream_tasks:
    - type: transcription
      condition: audio_detected
    - type: ai_tagging
      condition: metadata_extracted
---

# Media Ingestion Task: Podcast Episode

This task demonstrates how media ingestion can trigger downstream processing tasks.

## Workflow
1. Extract metadata from audio file
2. If audio streams detected → create transcription task
3. If metadata extracted successfully → create AI tagging task

## Expected Metadata
- Audio codec (likely MP3)
- Duration for transcription planning
- Embedded ID3 tags (title, artist, album)
- Bitrate and sample rate

## Downstream Integration
The `dependents` field ensures that transcription and tagging tasks are created automatically after successful metadata extraction, enabling the full autonomous media processing pipeline.
