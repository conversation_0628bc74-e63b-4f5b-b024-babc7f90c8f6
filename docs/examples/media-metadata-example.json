{"example_metadata_output": {"description": "Example of MediaMetadata structure that would be stored in media_metadata.metadata_json", "video_file_example": {"filename": "The.Matrix.1999.mkv", "filepath": "C:/Users/<USER>/Sync/AI Drop Box/media/movies/The.Matrix.1999.mkv", "filesize": 2147483648, "file_hash": "sha256:a1b2c3d4e5f6...", "format": "<PERSON><PERSON><PERSON>", "duration": 8160.5, "bitrate": 2100000, "video": [{"codec": "H.264", "resolution": "1920x1080", "width": 1920, "height": 1080, "fps": 23.976, "bitrate": 1800000}], "audio": [{"codec": "DTS", "channels": 6, "sample_rate": 48000, "bitrate": 1536000, "language": "en"}, {"codec": "AC-3", "channels": 2, "sample_rate": 48000, "bitrate": 192000, "language": "en"}], "subtitles": [{"codec": "SubRip", "language": "en", "forced": false}, {"codec": "SubRip", "language": "es", "forced": false}], "title": "The Matrix", "year": 1999, "genre": "Science Fiction", "description": "A computer programmer discovers reality is a simulation", "guessed_type": "movie", "confidence": 0.95}, "audio_file_example": {"filename": "song.flac", "filepath": "C:/Users/<USER>/Sync/AI Drop Box/media/music/album/song.flac", "filesize": 45000000, "file_hash": "sha256:f1e2d3c4b5a6...", "format": "flac", "duration": 245.7, "bitrate": 1411200, "audio": [{"codec": "FLAC", "channels": 2, "sample_rate": 44100, "bitrate": 1411200}], "title": "Bohemian Rhapsody", "artist": "Queen", "album": "A Night at the Opera", "year": 1975, "genre": "Rock", "guessed_type": "music", "confidence": 0.99}, "podcast_example": {"filename": "tech-talk-ep-42.mp3", "filepath": "C:/Users/<USER>/Sync/AI Drop Box/media/podcasts/tech-talk-ep-42.mp3", "filesize": 72000000, "file_hash": "sha256:9z8y7x6w5v4u...", "format": "mp3", "duration": 3600.0, "bitrate": 160000, "audio": [{"codec": "MP3", "channels": 2, "sample_rate": 44100, "bitrate": 160000}], "title": "The Future of AI Development", "artist": "Tech Talk Podcast", "album": "Tech Talk 2024", "year": 2024, "genre": "Podcast", "description": "Discussion about AI development trends and future predictions", "guessed_type": "podcast", "confidence": 0.87}}, "task_result_summary_example": {"description": "Example of what would be stored in tasks.result_summary for dashboard display", "success": true, "tool_used": "ffprobe", "extraction_time_ms": 1250, "file_type": "video", "duration": "2h 16m", "resolution": "1920x1080", "audio_tracks": 2, "subtitle_tracks": 2, "metadata_fields_extracted": 15, "guessed_content_type": "movie (95% confidence)"}}