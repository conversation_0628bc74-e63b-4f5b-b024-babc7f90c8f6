
# Home Media Intelligence System: ChromaDB & Meilisearch Integration PRD

## Feature Overview

AI-driven tagging will be added across all media types to label content with human-readable keywords (e.g. “kids”, “horror”, “90s”, “baby crawling”).  AI-generated tags simplify media management and search by auto-annotating content with relevant metadata.  The system will use a modular pipeline: audio from videos will be transcribed (Whisper) and analyzed; video frames or images will be embedded/classified (e.g. CLIP or OpenCLIP); and text and other features will be sent to local LLMs (via Ollama) to generate descriptive tags and explanations.  Tags will be stored and indexed so that users can query by keywords or by semantic similarity.  Aiming for explainability, the LLM will optionally annotate each tag with its reasoning, and the CLI will allow users to view and edit tags.  All processing is on-device (edge) for privacy – media never leaves the local system.

## Components & Responsibilities

* **File Watcher / Ingestion**: Monitors media folders and enqueues tasks. Detects new/changed files and triggers the pipeline.
* **Task Queue / Orchestrator**: Manages asynchronous tasks. For each file it will schedule subtasks: transcription (`media_transcribe`), tagging (`media_tag`), and indexing (`index_meili`, `index_chroma`). Each task type has a handler that performs its function.
* **Whisper ASR**: Converts video/audio to text. Whisper is a robust ASR model trained on 680K hours of audio. Its transcripts provide dialog and narration for later tagging.
* **Image/Video Embedder**: Extracts key frames from videos (e.g. scene boundaries or periodic snapshots). Each frame is processed by a vision encoder like CLIP (Contrastive Language–Image Pretraining) or similar. CLIP maps images into the same embedding space as text, enabling it to recognize and semantically describe visuals. This component produces embeddings or feature labels for visual content.
* **Audio Embedder**: (Optional) Computes audio embeddings (e.g. using Mel spectrogram-based models) to detect sounds or classify music. Audio embeddings turn sounds into vectors that capture audio characteristics. These can be used for similarity search (e.g. humming-based music lookup) and to help tag audio features.
* **LLM Tagger (Ollama)**: Uses a local LLM to analyze transcripts, frame descriptions, and context. The LLM suggests tags, ensures consistency (e.g. deduplicating synonyms), and can explain its reasoning. Ollama is a lightweight local framework for running LLMs (supporting models up to \~13B on our hardware). We will provide default prompts to generate category tags (genres, subjects, era) and content tags (actions, people).
* **Search Indexers**: Two indexers run after tagging. The **Meilisearch Indexer** writes document records (with fields like title, transcript, tags, summary) to a Meilisearch index. Meilisearch is a fast full-text engine that supports keyword queries and filters. We will mark tag fields (e.g. genre, subject tags) as *filterable attributes* so CLI searches can use filters like `tag = "Horror"`. The **ChromaDB Indexer** computes a semantic embedding for each media item (e.g. by embedding the transcript or a summary text in a vector space) and stores it in ChromaDB. ChromaDB is an open-source vector database for embedding search. Users will be able to query semantically similar content (e.g. “find videos like this one”) via cosine search in Chroma.
* **Database**: The existing media metadata store (e.g. SQLite or similar) will gain new fields for tag metadata and summaries. Each media record should include a tag list (array of strings) and an optional summary/description. Task status and failure logs can also be added.
* **CLI Interface**: A set of commands for users/administrators to manage and query media. Examples: `media import` (force-reingestion), `media tag <file>` (re-run tagging), `media index` (re-index for search), `media list-tags <file>`, `media search --query "kids horror"`, and `media find-similar <file>`. Commands for inspecting and retrying tasks (e.g. `media tasks list`) will help troubleshoot. The CLI should allow applying filters on tags (`--filter genre:Kids`) and querying full-text (via Meili) or semantic similarity (via Chroma). It should also allow viewing LLM-generated explanations for each tag and editing tags before confirming.

## Data Flows & Task Orchestration

1. **File Detection**: The file watcher detects new media and creates a `media_ingest` task.
2. **Ingestion**: The media metadata (file path, filename, format, metadata like duration) is recorded in the DB.
3. **Transcription Task**: A `media_transcribe` task runs Whisper on audio, splits audio into 30s chunks for encoding, and saves the full transcript text.
4. **Tagging Task**: A `media_tag` task uses the transcript plus visual features to generate tags. Steps include:

   * *Frame Extraction*: Sample key frames (e.g. every N seconds or on scene changes).
   * *Vision Encoding*: Run each frame through CLIP or another vision model to get labels or embed vectors. Aggregate common concepts.
   * *Audio Encoding*: Compute audio embeddings (optional step for sound cues).
   * *LLM Analysis*: Send transcript and top vision keywords into the LLM prompt, asking for tags (with category suggestions). The LLM returns tags and optional explanations.
   * *Tag Curation*: Post-process the list (remove duplicates, normalize synonyms, score confidence). Store tags (and tag confidences if desired) in the media DB.
5. **Indexing Tasks**: Once tags are saved, two indexing subtasks run:

   * *`index_meili`*: Prepares a JSON document combining metadata (title, description, transcript snippets), and tags. Ensures tag fields (arrays) are added to `filterableAttributes` in Meilisearch. Pushes to Meili. Meili will handle tokenization and inverted-index creation for free-text and tag filters.
   * *`index_chroma`*: Forms or updates a Chroma collection (e.g. “media”). Computes or updates the vector for semantic search (e.g. an embedding of the transcript or summary). Also stores metadata pointers (ID, filepath, tags) in Chroma for context. Chroma allows future queries like “find items similar to this embedding”.
6. **Querying**: When a user runs `media search` via CLI, the command decides whether to use Meili (text keyword search with optional `filter`) or Chroma (semantic search by query text or by example). Results display media items and their tags. Debug commands like `media tasks status` will use Chroma/Meili APIs to inspect indexing tasks.

## Schema Enhancements

* **Media Table**: Add fields such as `tags` (string array), `summary` (text), `language` (from ASR), and `transcript` (full or snippet). Optionally add `chroma_id` or a flag marking indexed status.
* **Tag Vocabulary (optional)**: If normalization is needed, maintain a dictionary table of tags (id, name) linked to media via many-to-many. However, storing tags as raw arrays in JSON is sufficient for Meili filtering and for display.
* **Search Index (Meilisearch)**: Create a dedicated index (e.g. `media_index`). Include attributes: `id`, `title`, `description`, `tags` (array), `transcript_snippet`. In settings, mark `tags` (and other facets like year, rating if available) as filterable. This enables queries like `q="" filter="tags:Kids AND tags:Horror"`.
* **ChromaDB Collections**: Use one or more Chroma collections (e.g. “media\_embeddings”). For each media ID, store the vector (embedding) and metadata including tags and filepath. ChromaDB will handle vector indexing for similarity queries.

## Integration with Meilisearch & ChromaDB

**Meilisearch** will hold human-friendly metadata for fast text/tag queries. It uses an inverted index to map words/tags to documents. We will push each media as a “document” in Meili so users can search by keyword in transcripts or filter by tag. Filters must be pre-declared (e.g. tags, genre, year). When Meilisearch receives a search request, it tokenizes the query and scans the index for matches, ranking by relevance.
**ChromaDB** will serve semantic similarity. After embedding each media’s content, Chroma can answer vector queries (e.g. embed a query phrase and find nearest neighbors). ChromaDB is explicitly designed for LLM integration and storing embedding-IDs pairs. For example, a query “funny baby crawling” can be converted to an embedding and run against Chroma to find videos that are semantically close, even if the exact words aren’t in their transcripts. Chroma’s new multimodal features even allow image+text RAG, so in the future we could embed keyframe images as well. Both systems work together: Meili gives exact keyword/filter matches (deterministic search), while Chroma gives similarity matches.

## CLI User Experience

The CLI will provide commands like:

* `media-tag --file <path>`: Manually trigger the tagging pipeline for a file. Shows progress and results (tags and explanations).
* `media-tags <id>`: List tags for a media item (with optional `--explain` to show LLM reasoning).
* `media-search [query] [--filter <expr>]`: Search by keywords or phrases. By default uses Meilisearch; a flag `--semantic` could use Chroma for similarity. E.g. `media-search "funny cat"`, `media-search "" --filter "tags:Comedy AND tags:Kids"`.
* `media-find-similar <file>`: Embeds the given file’s content and queries Chroma for similar items.
* `media-index-status`: Show status of indexing tasks, including Meili and Chroma tasks (both have task APIs).
* `media-tag-edit <id> [add|remove] <tag>`: Allow user to modify the tag list post-generation.
* `media-summary <id>`: Generate or display a short summary of the content (future feature).
  All CLI commands should be synchronous or show progress bars as tasks can be long. Output should highlight key info (e.g. tags, confidence, search results) in a human-readable table or list.

## Testing and Validation Strategy

* **Unit Tests**: Each component (e.g. transcript parsing, CLIP embedding, LLM prompt) has unit tests. For example, feeding a known image to CLIP should detect an expected concept. Test whisper output on sample audio clips.
* **Integration Tests**: Run end-to-end on sample media. For example, take a test video known to have “baby crawling in park”, run the full pipeline, and assert the tag list contains “baby” and “crawling”. Use automated queries to check Meili returns it for “baby”, and Chroma lists it under a similar query.
* **Accuracy Metrics**: Optionally, maintain a small annotated dataset to compute precision/recall of tags (compare AI tags vs human tags). This helps tune thresholds or prompt engineering for the LLM.
* **Performance Testing**: On the target hardware (32GB RAM, 8GB VRAM), measure processing time and memory for each task. Ensure defaults (like batching frames or chunk sizes) keep memory usage reasonable. Adjust (for instance, use 7B LLM models for \~8GB VRAM usage; 13B for \~16GB RAM).
* **Privacy Checks**: Verify that no external calls are made (all models run locally) and that media content is not sent to third parties.

## Resource Defaults and Future Extensibility

With 32GB RAM and an 8GB GPU available, we recommend default models sized accordingly: e.g. a 7B or 13B LLM (requires \~8–16GB RAM) and a CLIP variant with suitable VRAM usage. Batch sizes for embedding should be tuned to GPU memory. The architecture is modular: future tasks like automatic summarization, content recommendations (e.g. “watch next” using tag similarity), or additional AI features can plug into the pipeline. All design choices respect privacy by keeping data local (an “Edge AI” design for video) and by making tag reasoning transparent and user-editable.

**Sources:** Modern tagging and search rely on full-text and semantic embedding engines, transformer models like Whisper for transcription, CLIP for image understanding, and LLM frameworks like Ollama. Existing documentation and guides on Meilisearch filtering and ChromaDB usage have informed the integration approach. The emphasis on local processing aligns with privacy-preserving “edge AI” best practices.
