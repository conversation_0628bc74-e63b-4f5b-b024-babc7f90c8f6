#!/usr/bin/env bun

import { initDatabase, getDatabase } from './src/db';

async function checkAnalytics() {
    await initDatabase();
    const db = getDatabase();
    
    console.log('📊 Search Analytics Data:\n');
    
    const analytics = db.query('SELECT * FROM search_analytics ORDER BY timestamp DESC LIMIT 10').all();
    
    if (analytics.length === 0) {
        console.log('No search analytics found yet.');
        console.log('Run some searches with the smart search features to generate analytics.');
    } else {
        console.log(`Found ${analytics.length} search records:\n`);
        analytics.forEach((record: any, i) => {
            const date = new Date(record.timestamp);
            console.log(`${i + 1}. Query: "${record.query}"`);
            console.log(`   Results: ${record.results_count}`);
            console.log(`   Processing time: ${record.processing_time_ms}ms`);
            console.log(`   Time: ${date.toLocaleString()}`);
            if (record.filters) {
                console.log(`   Filters: ${record.filters}`);
            }
            console.log('');
        });
    }
    
    // Check if search_feedback table exists and has data
    try {
        const feedback = db.query('SELECT * FROM search_feedback ORDER BY timestamp DESC LIMIT 5').all();
        console.log(`📝 Search Feedback: ${feedback.length} records`);
        if (feedback.length > 0) {
            feedback.forEach((record: any, i) => {
                console.log(`${i + 1}. Search ID: ${record.search_id}`);
                console.log(`   Notes: ${record.feedback_notes || 'None'}`);
                console.log('');
            });
        }
    } catch (error) {
        console.log('📝 Search Feedback: Table not accessible');
    }
}

checkAnalytics().catch(console.error);
