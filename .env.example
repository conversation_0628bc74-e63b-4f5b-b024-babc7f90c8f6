# OpenAI Configuration
OPENAI_API_KEY=

# Ollama Configuration
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=qwen3:8b
OLLAMA_FAST_MODEL=llama3.2:3b

# ChromaDB Configuration
CHROMA_URL=http://localhost:8000
CHROMA_TENANT=default_tenant

# Meilisearch Configuration
MEILISEARCH_URL=http://localhost:7700
MEILISEARCH_MASTER_KEY=
MEILISEARCH_INDEX_NAME=media_index

# Whisper Configuration
WHISPER_MODEL=turbo
WHISPER_DEVICE=cpu
WHISPER_LANGUAGE=auto
WHISPER_CHUNK_DURATION=30

# Vision/CLIP Configuration
VISION_MODEL=openai/clip-vit-base-patch32
FRAME_INTERVAL_SECONDS=10
MAX_FRAMES_PER_VIDEO=50
ENABLE_SCENE_DETECTION=false

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
S3_ENDPOINT=
S3_DEFAULT_BUCKET=
S3_DEFAULT_DOWNLOAD_PATH=E:\S3\kesterson-photos
S3_SYNC_LOG_PATH=C:\Users\<USER>\Sync\AI Drop Box\logs\s3_sync

# Media Collection Paths
MEDIA_COLLECTION_TV=E:/Shows
MEDIA_COLLECTION_MOVIES=E:/Movies
MEDIA_COLLECTION_YOUTUBE=E:/YouTube
MEDIA_COLLECTION_CATCHALL=E:/Downloads (temp area)

# Media Tool Paths
FFPROBE_PATH=ffprobe
MEDIAINFO_PATH=mediainfo

# ===== CONTENT DISCOVERY AND DOWNLOADER INTEGRATION =====

# yt-dlp Configuration
YTDLP_PATH=yt-dlp
YTDLP_DEFAULT_FORMAT=best[height<=1080]
YTDLP_DEFAULT_QUALITY=720p
YTDLP_OUTPUT_TEMPLATE=%(title)s [%(id)s].%(ext)s

# Sonarr Configuration (TV Shows)
SONARR_BASE_URL=http://localhost:8989
SONARR_API_KEY=your_sonarr_api_key_here
SONARR_ENABLED=true

# Radarr Configuration (Movies)
RADARR_BASE_URL=http://localhost:7878
RADARR_API_KEY=your_radarr_api_key_here
RADARR_ENABLED=true

# Prowlarr Configuration (Indexer Manager)
PROWLARR_BASE_URL=http://localhost:9696
PROWLARR_API_KEY=your_prowlarr_api_key_here
PROWLARR_ENABLED=true

# qBittorrent Configuration (Optional)
QBITTORRENT_BASE_URL=http://localhost:8080
QBITTORRENT_USERNAME=admin
QBITTORRENT_PASSWORD=your_qbittorrent_password
QBITTORRENT_ENABLED=false

# RSS Configuration
RSS_ENABLED=true
RSS_CHECK_INTERVAL=3600
RSS_FEEDS=https://example.com/podcast.xml,https://example.com/video-feed.xml
