/**
 * Test setup file for coverage reporting and test configuration
 * This file is loaded before running tests to set up the testing environment
 */

import { beforeAll, afterAll } from 'bun:test';

// Global test setup
beforeAll(async () => {
    // Set test environment
    process.env.NODE_ENV = 'test';
    
    // Ensure test directories exist
    const testDirs = [
        '/tmp/folder-watcher-test',
        '/tmp/folder-watcher-coverage'
    ];
    
    for (const dir of testDirs) {
        try {
            await Bun.write(`${dir}/.gitkeep`, '');
        } catch (error) {
            // Directory creation handled by individual tests
        }
    }
    
    console.log('🧪 Test environment initialized');
});

// Global test cleanup
afterAll(async () => {
    console.log('🧹 Test environment cleaned up');
});

// Export test utilities that can be used across test files
export const testUtils = {
    /**
     * Create a temporary test directory
     */
    async createTempDir(name: string): Promise<string> {
        const dir = `/tmp/folder-watcher-test-${name}-${Date.now()}`;
        await Bun.write(`${dir}/.gitkeep`, '');
        return dir;
    },
    
    /**
     * Clean up temporary test directory
     */
    async cleanupTempDir(dir: string): Promise<void> {
        try {
            await Bun.spawn(['rm', '-rf', dir]).exited;
        } catch (error) {
            console.warn(`Failed to cleanup temp dir ${dir}:`, error);
        }
    },
    
    /**
     * Wait for a condition to be true with timeout
     */
    async waitFor(condition: () => boolean | Promise<boolean>, timeout = 5000): Promise<void> {
        const start = Date.now();
        while (Date.now() - start < timeout) {
            if (await condition()) {
                return;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        throw new Error(`Condition not met within ${timeout}ms`);
    }
};
