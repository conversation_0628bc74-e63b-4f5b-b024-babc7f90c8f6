#!/usr/bin/env bun

/**
 * Direct test script for MeiliSearch MCP Server
 * 
 * This script tests the MeiliSearch MCP server functionality directly
 * without going through the main application.
 */

import { meilisearchService } from './src/services/meilisearch-service';
import { initDatabase } from './src/db';

async function testMeiliSearchMCPServer() {
    console.log('🧪 Testing MeiliSearch MCP Server\n');

    try {
        // Initialize database and services
        console.log('1. Initializing database...');
        await initDatabase();
        console.log('✅ Database initialized\n');

        console.log('2. Initializing MeiliSearch service...');
        await meilisearchService.initialize();
        console.log('✅ MeiliSearch service initialized\n');

        // Test basic search functionality
        console.log('3. Testing basic search...');
        const searchResult = await meilisearchService.search('test', { limit: 5 });
        console.log(`✅ Search completed: ${searchResult.hits.length} results in ${searchResult.processingTimeMs}ms\n`);

        // Test index stats
        console.log('4. Getting index stats...');
        const stats = await meilisearchService.getStats();
        console.log(`✅ Index stats: ${stats.numberOfDocuments} documents, ${(stats.databaseSize / 1024 / 1024).toFixed(2)} MB\n`);

        // Test adding a sample document
        console.log('5. Testing document indexing...');
        const sampleMetadata = {
            filename: 'test-video.mp4',
            filepath: '/test/test-video.mp4',
            filesize: 1024000,
            file_hash: 'test-hash-123',
            format: 'mp4',
            duration: 120,
            bitrate: 1000,
            title: 'Test Video',
            description: 'A test video for MCP testing',
            tags: ['test', 'mcp', 'video'],
            transcript: 'This is a test transcript for the MCP server testing.',
            guessed_type: 'video' as const,
            language: 'en'
        };

        const documentId = await meilisearchService.indexMedia(999, sampleMetadata);
        console.log(`✅ Document indexed with ID: ${documentId}\n`);

        // Test search with the new document
        console.log('6. Testing search with indexed document...');
        const testSearch = await meilisearchService.search('test video', { limit: 5 });
        console.log(`✅ Search found ${testSearch.hits.length} results`);
        
        if (testSearch.hits.length > 0) {
            const hit = testSearch.hits[0];
            console.log(`   First result: ${hit.title} (${hit.filename})`);
            console.log(`   Tags: ${hit.tags?.join(', ')}`);
            console.log(`   Transcript snippet: ${hit.transcript_snippet?.substring(0, 50)}...`);
        }
        console.log('');

        // Test filter search
        console.log('7. Testing filtered search...');
        const filteredSearch = await meilisearchService.search('test', {
            filter: 'guessed_type = "video"',
            limit: 5
        });
        console.log(`✅ Filtered search found ${filteredSearch.hits.length} video results\n`);

        console.log('🎉 All MeiliSearch tests passed!\n');

        // Now test the MCP server tools (simulated)
        console.log('8. Testing MCP server tools simulation...');
        
        // Simulate smart search
        console.log('   Testing smart search optimization...');
        const queryOptimizations = testQueryOptimization('vidoe test'); // intentional typo
        console.log(`   ✅ Query optimization: "${queryOptimizations.original}" → "${queryOptimizations.optimized}"`);
        
        // Simulate search suggestions
        console.log('   Testing search suggestions...');
        const suggestions = getSearchSuggestions('test');
        console.log(`   ✅ Found ${suggestions.length} suggestions: ${suggestions.join(', ')}`);
        
        // Simulate analytics
        console.log('   Testing analytics simulation...');
        const analytics = generateMockAnalytics();
        console.log(`   ✅ Analytics: ${analytics.totalSearches} searches, ${analytics.avgProcessingTime}ms avg time`);
        
        console.log('\n🎉 MeiliSearch MCP Server test completed successfully!');
        console.log('\n📋 Test Summary:');
        console.log('   ✅ Database initialization');
        console.log('   ✅ MeiliSearch service initialization');
        console.log('   ✅ Basic search functionality');
        console.log('   ✅ Index statistics');
        console.log('   ✅ Document indexing');
        console.log('   ✅ Search with indexed content');
        console.log('   ✅ Filtered search');
        console.log('   ✅ MCP tools simulation');
        console.log('\n🚀 Ready to test with actual MCP server!');

    } catch (error) {
        console.error('❌ Test failed:', error instanceof Error ? error.message : String(error));
        console.error('\n🔧 Troubleshooting:');
        console.error('   1. Make sure MeiliSearch is running: ./meilisearch.exe --db-path ./meilisearch_db --http-addr 127.0.0.1:7700');
        console.error('   2. Check MeiliSearch health: curl http://127.0.0.1:7700/health');
        console.error('   3. Verify database migrations: bun run migrate');
        process.exit(1);
    }
}

function testQueryOptimization(query: string): { original: string, optimized: string } {
    // Simple spell correction simulation
    const corrections: Record<string, string> = {
        'vidoe': 'video',
        'moive': 'movie',
        'documentry': 'documentary',
        'comdy': 'comedy',
        'horor': 'horror'
    };

    let optimized = query;
    for (const [wrong, correct] of Object.entries(corrections)) {
        optimized = optimized.replace(new RegExp(wrong, 'gi'), correct);
    }

    return { original: query, optimized };
}

function getSearchSuggestions(partialQuery: string): string[] {
    // Mock search suggestions
    const suggestions = [
        'test video',
        'test audio',
        'test movie',
        'test documentary',
        'test music'
    ];

    return suggestions.filter(s => s.includes(partialQuery.toLowerCase()));
}

function generateMockAnalytics() {
    return {
        totalSearches: 42,
        successfulSearches: 38,
        failedSearches: 4,
        avgProcessingTime: 125.5,
        avgResultsPerSearch: 8.2,
        topQueries: [
            { query: 'funny videos', frequency: 12 },
            { query: 'music', frequency: 8 },
            { query: 'documentary', frequency: 6 }
        ]
    };
}

// Run the test
testMeiliSearchMCPServer().catch(console.error);
