# 🍌 Banana Bun - Linux Setup Guide

This guide covers the specific steps needed to run Banana Bun on Linux systems.

## Quick Start

1. **Run the setup script:**
   ```bash
   ./setup-linux.sh
   ```

2. **Configure environment:**
   ```bash
   # Edit the .env file to customize paths and add API keys
   nano .env
   ```

3. **Start services:**
   ```bash
   ./start-services-linux.sh
   ```

4. **Start Banana Bun:**
   ```bash
   bun run dev
   ```

## Manual Installation

If you prefer to install manually:

### Prerequisites

```bash
# Update system
sudo apt update

# Install system dependencies
sudo apt install -y python3 python3-pip ffmpeg mediainfo curl wget

# Install Bun (JavaScript runtime)
curl -fsSL https://bun.sh/install.sh | bash
source ~/.bashrc

# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Install MeiliSearch
wget https://github.com/meilisearch/meilisearch/releases/latest/download/meilisearch-linux-amd64
chmod +x meilisearch-linux-amd64
sudo mv meilisearch-linux-amd64 /usr/local/bin/meilisearch

# Install Python packages
pip3 install --user openai-whisper chromadb yt-dlp
```

### Project Setup

```bash
# Install Node.js dependencies
bun install

# Create directories
mkdir -p ~/banana-bun-data/{incoming,processing,archive,error,tasks,outputs,logs,dashboard,media}
mkdir -p ~/media/{Shows,Movies,YouTube,Downloads}

# Copy and edit environment file
cp .env.linux .env
nano .env  # Edit paths and add API keys
```

## Configuration

### Environment Variables

Key variables to customize in `.env`:

```bash
# Base paths - adjust to your preference
BASE_PATH=/home/<USER>/banana-bun-data
MEDIA_COLLECTION_PATH=/home/<USER>/media

# Media collection paths
MEDIA_COLLECTION_TV=/home/<USER>/media/Shows
MEDIA_COLLECTION_MOVIES=/home/<USER>/media/Movies
MEDIA_COLLECTION_YOUTUBE=/home/<USER>/media/YouTube
MEDIA_COLLECTION_CATCHALL=/home/<USER>/media/Downloads

# API keys (optional but recommended)
OPENAI_API_KEY=your_openai_key_here
```

### Service Configuration

The system requires these external services:

1. **Ollama** (localhost:11434) - Local LLM processing
2. **ChromaDB** (localhost:8000) - Vector database for embeddings
3. **MeiliSearch** (localhost:7700) - Full-text search engine

## Running the System

### Start Services

Use the provided script:
```bash
./start-services-linux.sh
```

Or start manually:
```bash
# Start Ollama
ollama serve &

# Start ChromaDB
chroma run --path ./chroma_db --port 8000 &

# Start MeiliSearch
meilisearch --db-path ./meilisearch_db --http-addr 127.0.0.1:7700 &

# Pull required models
ollama pull qwen3:8b
```

### Start Banana Bun

```bash
bun run dev
```

### Health Checks

Verify services are running:
```bash
curl http://localhost:11434/api/tags      # Ollama
curl http://localhost:8000/api/v1/heartbeat  # ChromaDB
curl http://localhost:7700/health         # MeiliSearch
```

## Usage Examples

### Media Processing

```bash
# Ingest a video file
npm run media-ingest /path/to/video.mp4

# Search for content
npm run media-search "funny cat videos"
npm run media-search --semantic "baby crawling"

# Manage tags
npm run media-tags list /path/to/video.mp4
npm run media-tags add /path/to/video.mp4 "comedy" "family"
```

### File Monitoring

Drop files into the watched directories:
- `~/banana-bun-data/incoming/` - New files to process
- Files will be automatically moved through the pipeline

## Troubleshooting

### Common Issues

1. **Permission denied errors:**
   ```bash
   chmod +x setup-linux.sh start-services-linux.sh stop-services-linux.sh
   ```

2. **Python module not found:**
   ```bash
   pip3 install --user openai-whisper chromadb
   # Add ~/.local/bin to PATH if needed
   echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
   source ~/.bashrc
   ```

3. **Service won't start:**
   ```bash
   # Check if ports are in use
   sudo netstat -tlnp | grep -E ':(7700|8000|11434)'
   
   # Kill existing processes if needed
   pkill -f meilisearch
   pkill -f chroma
   pkill -f ollama
   ```

4. **FFmpeg not found:**
   ```bash
   sudo apt install ffmpeg
   # Verify installation
   ffprobe -version
   ```

### Logs

Check service logs:
```bash
# Banana Bun logs
tail -f ~/banana-bun-data/logs/*.log

# System logs for services
journalctl -u ollama -f  # If using systemd service
```

## Differences from Windows

- Uses Linux-style paths (`/home/<USER>/` instead of `C:\Users\<USER>