#!/usr/bin/env bun

/**
 * Direct MCP Server Communication Test
 * 
 * This script tests the MeiliSearch MCP server by sending JSON-RPC messages directly
 */

import { spawn } from 'child_process';

interface MCPRequest {
    jsonrpc: string;
    id: number;
    method: string;
    params?: any;
}

interface MCPResponse {
    jsonrpc: string;
    id: number;
    result?: any;
    error?: any;
}

class MCPTester {
    private process: any;
    private requestId = 1;

    async startServer(): Promise<void> {
        console.log('🚀 Starting MeiliSearch MCP server...');
        
        this.process = spawn('bun', ['run', 'src/mcp/meilisearch-server.ts'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            cwd: process.cwd()
        });

        // Wait for server to start
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log('✅ MCP server started\n');
    }

    async sendRequest(method: string, params?: any): Promise<MCPResponse> {
        const request: MCPRequest = {
            jsonrpc: '2.0',
            id: this.requestId++,
            method,
            params
        };

        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Request timeout'));
            }, 10000);

            this.process.stdout.once('data', (data: Buffer) => {
                clearTimeout(timeout);
                try {
                    const response = JSON.parse(data.toString());
                    resolve(response);
                } catch (error) {
                    reject(new Error(`Failed to parse response: ${data.toString()}`));
                }
            });

            this.process.stdin.write(JSON.stringify(request) + '\n');
        });
    }

    async testInitialization(): Promise<void> {
        console.log('1. Testing server initialization...');
        
        const initResponse = await this.sendRequest('initialize', {
            protocolVersion: '2024-11-05',
            capabilities: { tools: {} },
            clientInfo: { name: 'test-client', version: '1.0.0' }
        });

        if (initResponse.error) {
            throw new Error(`Initialization failed: ${JSON.stringify(initResponse.error)}`);
        }

        console.log('✅ Server initialized successfully\n');
    }

    async testListTools(): Promise<void> {
        console.log('2. Testing tools listing...');
        
        const toolsResponse = await this.sendRequest('tools/list');

        if (toolsResponse.error) {
            throw new Error(`Tools listing failed: ${JSON.stringify(toolsResponse.error)}`);
        }

        const tools = toolsResponse.result?.tools || [];
        console.log(`✅ Found ${tools.length} tools:`);
        tools.forEach((tool: any) => {
            console.log(`   - ${tool.name}: ${tool.description}`);
        });
        console.log('');
    }

    async testSmartSearch(): Promise<void> {
        console.log('3. Testing smart search...');
        
        const searchResponse = await this.sendRequest('tools/call', {
            name: 'smart_search',
            arguments: {
                query: 'test video',
                limit: 5,
                optimize_query: true,
                learn_from_search: true
            }
        });

        if (searchResponse.error) {
            throw new Error(`Smart search failed: ${JSON.stringify(searchResponse.error)}`);
        }

        const result = JSON.parse(searchResponse.result?.content?.[0]?.text || '{}');
        console.log(`✅ Smart search completed:`);
        console.log(`   Query: "${result.original_query}" → "${result.final_query}"`);
        console.log(`   Results: ${result.results?.hits?.length || 0} hits`);
        console.log(`   Processing time: ${result.results?.processing_time_ms || 0}ms`);
        if (result.optimization_applied) {
            console.log(`   Optimization: ${result.optimization_applied.optimization_type}`);
        }
        console.log('');
    }

    async testSearchSuggestions(): Promise<void> {
        console.log('4. Testing search suggestions...');
        
        const suggestionsResponse = await this.sendRequest('tools/call', {
            name: 'get_search_suggestions',
            arguments: {
                partial_query: 'test',
                limit: 5
            }
        });

        if (suggestionsResponse.error) {
            throw new Error(`Search suggestions failed: ${JSON.stringify(suggestionsResponse.error)}`);
        }

        const result = JSON.parse(suggestionsResponse.result?.content?.[0]?.text || '{}');
        console.log(`✅ Search suggestions:`);
        console.log(`   Query suggestions: ${result.query_suggestions?.length || 0}`);
        console.log(`   Filter suggestions: ${result.filter_suggestions?.length || 0}`);
        console.log('');
    }

    async testAnalytics(): Promise<void> {
        console.log('5. Testing search analytics...');
        
        const analyticsResponse = await this.sendRequest('tools/call', {
            name: 'get_search_analytics',
            arguments: {
                time_range_hours: 24
            }
        });

        if (analyticsResponse.error) {
            throw new Error(`Analytics failed: ${JSON.stringify(analyticsResponse.error)}`);
        }

        const result = JSON.parse(analyticsResponse.result?.content?.[0]?.text || '{}');
        console.log(`✅ Search analytics:`);
        console.log(`   Total searches: ${result.summary?.total_searches || 0}`);
        console.log(`   Successful: ${result.summary?.successful_searches || 0}`);
        console.log(`   Avg processing time: ${result.summary?.avg_processing_time?.toFixed(1) || 0}ms`);
        console.log('');
    }

    async cleanup(): Promise<void> {
        if (this.process) {
            this.process.kill();
            console.log('🧹 MCP server stopped');
        }
    }
}

async function runMCPTests() {
    const tester = new MCPTester();
    
    try {
        console.log('🧪 Testing MeiliSearch MCP Server Direct Communication\n');
        
        await tester.startServer();
        await tester.testInitialization();
        await tester.testListTools();
        await tester.testSmartSearch();
        await tester.testSearchSuggestions();
        await tester.testAnalytics();
        
        console.log('🎉 All MCP tests completed successfully!\n');
        console.log('📋 Test Summary:');
        console.log('   ✅ Server initialization');
        console.log('   ✅ Tools listing');
        console.log('   ✅ Smart search');
        console.log('   ✅ Search suggestions');
        console.log('   ✅ Search analytics');
        
    } catch (error) {
        console.error('❌ MCP test failed:', error instanceof Error ? error.message : String(error));
        console.error('\n🔧 Troubleshooting:');
        console.error('   1. Make sure MeiliSearch is running');
        console.error('   2. Check database migrations are applied');
        console.error('   3. Verify MCP server can start independently');
        process.exit(1);
    } finally {
        await tester.cleanup();
    }
}

// Run the tests
runMCPTests().catch(console.error);
