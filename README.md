# 🍌 Banana Bun

> *A bun-sized AI assistant for organizing your digital life.*

Banana Bun is a whimsical, developer-first project that uses **local AI models** to help users automatically **tag, organize, and search their media files** — audio, video, images, or docs — all while keeping privacy at the core. Built on Bun for speed and simplicity, it combines local LLMs, computer vision, and vector search into a single elegant pipeline.

## ✨ Features

| Feature | Description | Why It's Special |
|---------|-------------|------------------|
| 🎙 **AI Transcription** | Whisper-powered speech-to-text for videos & audio | Local processing, no cloud dependency |
| 🏷 **Smart Tagging** | LLM-generated tags with explanations | Context-aware, human-readable descriptions |
| 🔍 **Hybrid Search** | Vector + text search across your media | Find content by meaning, not just keywords |
| 🗂 **Vector & Text Search** | ChromaDB + Meilisearch for hybrid search | Full-text and vector search engineering |
| 📚 **Semantic Archive** | Organizes by concept, date, or AI-extracted theme | Content enrichment + NLP workflows |
| 🔧 **CLI Tools** | Fully scriptable command line interface | Dev tool design and DX principles |
| 🛡 **Privacy-first** | No outbound data by default | Ethical dev and local-first focus |
| 🍞 **Bun-based Runtime** | Built on Bun for speed and simplicity | Early adoption of modern runtime tech |

## 🎯 Use Cases

### For Developers
- Keep a searchable archive of your coding livestreams or talks
- Auto-transcribe & tag tech podcasts and tutorials

### For Creatives
- Smart moodboard: auto-organize image & video inspiration
- Search your own video clips by "keywords that never existed"

### For Everyday Users
- Turn your digital hoard into a smart, searchable archive
- Keep private transcripts of family videos or interviews

## 🚀 Quick Start

### Prerequisites

- **Bun** runtime (latest version)
- **FFmpeg** for media processing
- **yt-dlp** for YouTube downloads (optional)
- **Local AI models** (Whisper, CLIP, etc.)

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/banana-bun.git
cd banana-bun

# Install dependencies
bun install

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Initialize the database
bun run migrate

# Start the system
bun run dev
```

### Basic Usage

```bash
# Ingest a media file
bun run media-ingest /path/to/your/video.mp4

# Search your media
bun run media-search "funny cat videos"
bun run media-search --semantic "emotional moments"

# Download and process YouTube content
bun run src/cli/download-media.ts --source youtube --url "https://youtube.com/watch?v=..."

# Organize your media library
bun run media-organize /path/to/media/folder
```

## 🛠 Architecture

Banana Bun follows a modular, task-based architecture:

- **Task Orchestrator**: Manages the processing pipeline
- **Media Processors**: Handle different file types (video, audio, images)
- **AI Services**: Local LLM and embedding generation
- **Search Engine**: Hybrid vector + text search
- **CLI Tools**: Developer-friendly command interface

## 📁 Project Structure

```
banana-bun/
├── src/
│   ├── cli/           # Command-line tools
│   ├── executors/     # Task execution logic
│   ├── services/      # AI and processing services
│   ├── mcp/          # Model Context Protocol servers
│   └── utils/        # Helper functions
├── docs/             # Documentation
├── test/             # Test suites
└── scripts/          # Build and utility scripts
```

## 🔧 Development

### Running Tests

```bash
# Run all tests
bun test

# Run tests with coverage
bun run test:report

# Run specific test suites
bun test test/media-intelligence.test.ts
```

### CLI Commands

```bash
# Media processing
bun run media-ingest /path/to/file.mp4
bun run media-search "search query"
bun run media-tags list /path/to/file.mp4

# Advanced AI features
bun run banana-summarize --media 123
bun run banana-recommend --user alice --top 10
bun run banana-detect-scenes --media 123
```

## 🤝 Contributing

Banana Bun is designed to be developer-friendly and extensible:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** and add tests
4. **Run the test suite**: `bun test`
5. **Submit a pull request**

## 🙏 Acknowledgments

Banana Bun is inspired by and builds upon the excellent work of:
- **Tagger** - Media tagging workflows
- **Transcription Stream** - Audio processing pipelines
- **PhotoPrism** - Media organization concepts
- **Immich** - Self-hosted media management
- **OpenL3/CLIP** - Multimodal AI embeddings

## 🍌 Why "Banana Bun"?

Because organizing your digital life should be as delightful as a warm, sweet bun! 🥐✨

---

*Built with ❤️ using Bun, local AI, and a commitment to privacy-first development.*
